import React from 'react';
import { Link } from 'react-router-dom';

const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-blue to-secondary-blue text-white py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 fade-in">
              منصة علاء عبد الحميد التعليمية
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100 fade-in">
              تعلم التسويق متعدد المستويات من الخبراء واحصل على شهادات معتمدة
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center fade-in">
              <Link to="/login" className="btn btn-gold btn-lg">
                <i className="fas fa-sign-in-alt"></i>
                ابدأ التعلم الآن
              </Link>
              <a href="#courses" className="btn btn-outline btn-lg text-white border-white hover:bg-white hover:text-primary-blue">
                <i className="fas fa-book"></i>
                استكشف الدورات
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-text-dark mb-4">
              لماذا تختار منصتنا؟
            </h2>
            <p className="text-lg text-text-light max-w-2xl mx-auto">
              نقدم تجربة تعليمية متميزة مع أحدث التقنيات والمحتوى عالي الجودة
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="card text-center">
              <div className="w-16 h-16 bg-light-blue rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-video text-primary-blue text-2xl"></i>
              </div>
              <h3 className="text-xl font-semibold mb-3">محتوى فيديو عالي الجودة</h3>
              <p className="text-text-light">
                دروس فيديو احترافية مع شرح مفصل وأمثلة عملية لضمان الفهم الكامل
              </p>
            </div>

            <div className="card text-center">
              <div className="w-16 h-16 bg-light-blue rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-certificate text-primary-blue text-2xl"></i>
              </div>
              <h3 className="text-xl font-semibold mb-3">شهادات معتمدة</h3>
              <p className="text-text-light">
                احصل على شهادات معتمدة عند إتمام الدورات بنجاح لتعزيز مسيرتك المهنية
              </p>
            </div>

            <div className="card text-center">
              <div className="w-16 h-16 bg-light-blue rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-users text-primary-blue text-2xl"></i>
              </div>
              <h3 className="text-xl font-semibold mb-3">دعم مستمر</h3>
              <p className="text-text-light">
                فريق دعم متخصص متاح لمساعدتك في رحلتك التعليمية على مدار الساعة
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Courses Section */}
      <section id="courses" className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-text-dark mb-4">
              مستويات الدورات التدريبية
            </h2>
            <p className="text-lg text-text-light max-w-2xl mx-auto">
              برنامج تدريبي متدرج يأخذك من المبتدئ إلى الخبير في التسويق متعدد المستويات
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Level 1 */}
            <div className="card">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center ml-3">
                  <span className="text-green-600 font-bold text-lg">1</span>
                </div>
                <h3 className="text-xl font-semibold">المستوى الأول</h3>
              </div>
              <p className="text-text-light mb-4">
                أساسيات التسويق متعدد المستويات والمفاهيم الأساسية للبدء في هذا المجال
              </p>
              <ul className="space-y-2 text-sm text-text-light mb-6">
                <li className="flex items-center">
                  <i className="fas fa-check text-green-500 ml-2"></i>
                  مقدمة في التسويق متعدد المستويات
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-green-500 ml-2"></i>
                  بناء الشبكة الأساسية
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-green-500 ml-2"></i>
                  مهارات التواصل الأساسية
                </li>
              </ul>
              <div className="text-center">
                <span className="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                  للمبتدئين
                </span>
              </div>
            </div>

            {/* Level 2 */}
            <div className="card">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center ml-3">
                  <span className="text-blue-600 font-bold text-lg">2</span>
                </div>
                <h3 className="text-xl font-semibold">المستوى الثاني</h3>
              </div>
              <p className="text-text-light mb-4">
                استراتيجيات متقدمة في التسويق وبناء فرق العمل الفعالة
              </p>
              <ul className="space-y-2 text-sm text-text-light mb-6">
                <li className="flex items-center">
                  <i className="fas fa-check text-blue-500 ml-2"></i>
                  استراتيجيات التسويق المتقدمة
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-blue-500 ml-2"></i>
                  إدارة وتطوير الفرق
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-blue-500 ml-2"></i>
                  تحليل الأداء والنتائج
                </li>
              </ul>
              <div className="text-center">
                <span className="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                  متوسط
                </span>
              </div>
            </div>

            {/* Level 3 */}
            <div className="card">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center ml-3">
                  <span className="text-purple-600 font-bold text-lg">3</span>
                </div>
                <h3 className="text-xl font-semibold">المستوى الثالث</h3>
              </div>
              <p className="text-text-light mb-4">
                القيادة والإدارة المتقدمة وبناء إمبراطورية تسويقية ناجحة
              </p>
              <ul className="space-y-2 text-sm text-text-light mb-6">
                <li className="flex items-center">
                  <i className="fas fa-check text-purple-500 ml-2"></i>
                  القيادة والإدارة المتقدمة
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-purple-500 ml-2"></i>
                  بناء الأنظمة والعمليات
                </li>
                <li className="flex items-center">
                  <i className="fas fa-check text-purple-500 ml-2"></i>
                  التوسع والنمو المستدام
                </li>
              </ul>
              <div className="text-center">
                <span className="inline-block bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium">
                  متقدم
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-blue text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            ابدأ رحلتك التعليمية اليوم
          </h2>
          <p className="text-xl mb-8 text-blue-100">
            انضم إلى آلاف الطلاب الذين حققوا النجاح من خلال منصتنا
          </p>
          <Link to="/login" className="btn btn-gold btn-lg">
            <i className="fas fa-rocket"></i>
            ابدأ الآن
          </Link>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
