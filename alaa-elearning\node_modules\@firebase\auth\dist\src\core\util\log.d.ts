/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { LogLevel } from '@firebase/logger';
export { LogLevel };
export declare function _getLogLevel(): LogLevel;
export declare function _setLogLevel(newLevel: LogLevel): void;
export declare function _logDebug(msg: string, ...args: string[]): void;
export declare function _logWarn(msg: string, ...args: string[]): void;
export declare function _logError(msg: string, ...args: string[]): void;
