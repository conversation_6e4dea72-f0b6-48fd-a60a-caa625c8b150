import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { Course, CourseSection, Video, PDFFile } from '../types';
import { getCourses } from '../services/courseService';
import LoadingSpinner from '../components/LoadingSpinner';

const CourseView: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();

  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedSection, setSelectedSection] = useState<CourseSection | null>(null);
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  useEffect(() => {
    if (courseId) {
      loadCourse();
    }
  }, [courseId]);

  const loadCourse = async () => {
    try {
      const courses = await getCourses();
      const foundCourse = courses.find(c => c.id === courseId);

      if (!foundCourse) {
        navigate('/dashboard');
        return;
      }

      // Check if user is enrolled
      if (!user?.enrolledCourses.includes(foundCourse.id)) {
        navigate('/dashboard');
        return;
      }

      setCourse(foundCourse);

      // Auto-select first section and video
      if (foundCourse.sections.length > 0) {
        const firstSection = foundCourse.sections[0];
        setSelectedSection(firstSection);
        if (firstSection.videos.length > 0) {
          setSelectedVideo(firstSection.videos[0]);
        }
      }
    } catch (error) {
      console.error('Error loading course:', error);
      navigate('/dashboard');
    } finally {
      setLoading(false);
    }
  };

  const handleSectionSelect = (section: CourseSection) => {
    setSelectedSection(section);
    if (section.videos.length > 0) {
      setSelectedVideo(section.videos[0]);
    } else {
      setSelectedVideo(null);
    }
  };

  const handleVideoSelect = (video: Video) => {
    setSelectedVideo(video);
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Byte';
    const i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)).toString());
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  if (loading) {
    return <LoadingSpinner text="جاري تحميل الدورة..." />;
  }

  if (!course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <i className="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
          <h2 className="text-2xl font-bold text-gray-700 mb-2">الدورة غير موجودة</h2>
          <p className="text-gray-500 mb-4">لم يتم العثور على الدورة المطلوبة</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="btn btn-primary"
          >
            العودة للوحة الطالب
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <div className={`fixed right-0 top-0 h-full bg-white shadow-lg border-l border-gray-200 transition-all duration-300 z-40 ${
        sidebarOpen ? 'w-80' : 'w-16'
      }`}>
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-lg hover:bg-gray-100"
            >
              <i className="fas fa-bars text-gray-600"></i>
            </button>
            {sidebarOpen && (
              <button
                onClick={() => navigate('/dashboard')}
                className="text-sm text-blue-600 hover:text-blue-800"
              >
                <i className="fas fa-arrow-right ml-1"></i>
                العودة للوحة الطالب
              </button>
            )}
          </div>

          {sidebarOpen && (
            <div className="mt-4">
              <h2 className="font-bold text-gray-900 text-lg mb-1">
                {course.title}
              </h2>
              <p className="text-sm text-gray-500">
                {course.sections.length} قسم • المستوى {course.level}
              </p>
            </div>
          )}
        </div>

        {/* Course Sections */}
        {sidebarOpen && (
          <div className="overflow-y-auto h-full pb-20">
            {course.sections.map((section, sectionIndex) => (
              <div key={section.id} className="border-b border-gray-100">
                <button
                  onClick={() => handleSectionSelect(section)}
                  className={`w-full p-4 text-right hover:bg-gray-50 transition-colors ${
                    selectedSection?.id === section.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900 text-sm">
                        {sectionIndex + 1}. {section.title}
                      </h3>
                      <p className="text-xs text-gray-500 mt-1">
                        {section.videos.length} فيديو • {section.pdfs.length} ملف
                      </p>
                    </div>
                    <i className={`fas fa-chevron-${selectedSection?.id === section.id ? 'down' : 'left'} text-gray-400`}></i>
                  </div>
                </button>

                {/* Section Content */}
                {selectedSection?.id === section.id && (
                  <div className="bg-gray-50">
                    {/* Videos */}
                    {section.videos.map((video, videoIndex) => (
                      <button
                        key={video.id}
                        onClick={() => handleVideoSelect(video)}
                        className={`w-full p-3 pr-8 text-right hover:bg-gray-100 transition-colors ${
                          selectedVideo?.id === video.id ? 'bg-blue-100' : ''
                        }`}
                      >
                        <div className="flex items-center gap-3">
                          <i className="fas fa-play-circle text-blue-500"></i>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              {videoIndex + 1}. {video.title}
                            </p>
                            <p className="text-xs text-gray-500">
                              {formatDuration(video.duration)}
                            </p>
                          </div>
                        </div>
                      </button>
                    ))}

                    {/* PDFs */}
                    {section.pdfs.map((pdf, pdfIndex) => (
                      <a
                        key={pdf.id}
                        href={pdf.url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="block w-full p-3 pr-8 text-right hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <i className="fas fa-file-pdf text-red-500"></i>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-gray-900">
                              {pdf.title}
                            </p>
                            <p className="text-xs text-gray-500">
                              {formatFileSize(pdf.fileSize)}
                            </p>
                          </div>
                        </div>
                      </a>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'mr-80' : 'mr-16'}`}>
        {selectedVideo ? (
          <div className="h-full flex flex-col">
            {/* Video Player */}
            <div className="bg-black flex-1 flex items-center justify-center">
              <video
                key={selectedVideo.url}
                controls
                className="w-full h-full max-h-[70vh]"
                poster="/video-placeholder.jpg"
              >
                <source src={selectedVideo.url} type="video/mp4" />
                متصفحك لا يدعم تشغيل الفيديو
              </video>
            </div>

            {/* Video Info */}
            <div className="bg-white p-6 border-t border-gray-200">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {selectedVideo.title}
              </h1>
              {selectedVideo.description && (
                <p className="text-gray-600 mb-4">
                  {selectedVideo.description}
                </p>
              )}
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span>
                  <i className="fas fa-clock ml-1"></i>
                  {formatDuration(selectedVideo.duration)}
                </span>
                <span>
                  <i className="fas fa-folder ml-1"></i>
                  {selectedSection?.title}
                </span>
              </div>
            </div>
          </div>
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center">
              <i className="fas fa-play-circle text-gray-400 text-6xl mb-4"></i>
              <h2 className="text-xl font-semibold text-gray-700 mb-2">
                اختر فيديو للمشاهدة
              </h2>
              <p className="text-gray-500">
                اختر قسماً من الشريط الجانبي لبدء المشاهدة
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CourseView;
