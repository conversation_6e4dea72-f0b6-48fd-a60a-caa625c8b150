import React, { useEffect, useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';

interface SessionManagerProps {
  children: React.ReactNode;
}

const SessionManager: React.FC<SessionManagerProps> = ({ children }) => {
  const { user, admin, refreshUser } = useAuth();
  const [showSessionWarning, setShowSessionWarning] = useState(false);
  const [sessionTimeLeft, setSessionTimeLeft] = useState<number>(0);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    let warningTimeoutId: NodeJS.Timeout | undefined;

    const checkSession = () => {
      const userSession = localStorage.getItem('alaa_elearning_user');
      const adminSession = localStorage.getItem('alaa_elearning_admin');
      
      const activeSession = userSession || adminSession;
      
      if (activeSession) {
        try {
          const sessionData = JSON.parse(activeSession);
          const sessionAge = Date.now() - sessionData.timestamp;
          const sessionTimeout = 24 * 60 * 60 * 1000; // 24 hours
          const warningTime = sessionTimeout - (30 * 60 * 1000); // 30 minutes before expiry
          
          const timeLeft = sessionTimeout - sessionAge;
          setSessionTimeLeft(Math.max(0, Math.floor(timeLeft / 1000)));
          
          // Show warning 30 minutes before session expires
          if (sessionAge > warningTime && timeLeft > 0) {
            setShowSessionWarning(true);
          }
          
          // Auto-logout when session expires
          if (sessionAge >= sessionTimeout) {
            handleSessionExpiry();
          }
        } catch (error) {
          console.error('Session check error:', error);
        }
      }
    };

    const handleSessionExpiry = () => {
      localStorage.removeItem('alaa_elearning_user');
      localStorage.removeItem('alaa_elearning_admin');
      window.location.href = '/';
    };

    // Check session every minute
    if (user || admin) {
      intervalId = setInterval(checkSession, 60000);
      checkSession(); // Initial check
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
      if (warningTimeoutId) clearTimeout(warningTimeoutId);
    };
  }, [user, admin]);

  const extendSession = async () => {
    try {
      await refreshUser();
      setShowSessionWarning(false);
      
      // Update session timestamp
      const userSession = localStorage.getItem('alaa_elearning_user');
      const adminSession = localStorage.getItem('alaa_elearning_admin');
      
      if (userSession) {
        const sessionData = JSON.parse(userSession);
        sessionData.timestamp = Date.now();
        localStorage.setItem('alaa_elearning_user', JSON.stringify(sessionData));
      }
      
      if (adminSession) {
        const sessionData = JSON.parse(adminSession);
        sessionData.timestamp = Date.now();
        localStorage.setItem('alaa_elearning_admin', JSON.stringify(sessionData));
      }
    } catch (error) {
      console.error('Session extension error:', error);
    }
  };

  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <>
      {children}
      
      {/* Session Warning Modal */}
      {showSessionWarning && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-clock text-yellow-600 text-2xl"></i>
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                تحذير انتهاء الجلسة
              </h3>
              
              <p className="text-gray-600 mb-4">
                ستنتهي جلستك خلال {formatTime(sessionTimeLeft)}
              </p>
              
              <p className="text-sm text-gray-500 mb-6">
                هل تريد تمديد الجلسة؟
              </p>
              
              <div className="flex gap-3 justify-center">
                <button
                  onClick={extendSession}
                  className="btn btn-primary"
                >
                  <i className="fas fa-clock ml-2"></i>
                  تمديد الجلسة
                </button>
                
                <button
                  onClick={() => setShowSessionWarning(false)}
                  className="btn btn-outline"
                >
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Session Timer (for development/debugging) */}
      {(user || admin) && import.meta.env.DEV && (
        <div className="fixed bottom-4 left-4 bg-gray-800 text-white px-3 py-2 rounded-lg text-xs">
          <div>الجلسة: {formatTime(sessionTimeLeft)}</div>
          <div>{user ? 'طالب' : 'مدير'}</div>
        </div>
      )}
    </>
  );
};

export default SessionManager;
