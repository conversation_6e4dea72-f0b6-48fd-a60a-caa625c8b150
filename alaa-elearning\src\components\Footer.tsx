import React from 'react';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-text-dark text-white py-8 mt-auto">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* About Section */}
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <i className="fas fa-graduation-cap text-gold"></i>
              منصة علاء عبد الحميد التعليمية
            </h3>
            <p className="text-gray-300 text-sm leading-relaxed">
              منصة تعليمية احترافية متخصصة في دورات التسويق متعددة المستويات، 
              نقدم محتوى تعليمي عالي الجودة مع شهادات معتمدة.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <i className="fas fa-link text-gold"></i>
              روابط سريعة
            </h4>
            <ul className="space-y-2 text-sm">
              <li>
                <a href="/" className="text-gray-300 hover:text-gold transition-colors">
                  <i className="fas fa-home ml-2"></i>
                  الرئيسية
                </a>
              </li>
              <li>
                <a href="/login" className="text-gray-300 hover:text-gold transition-colors">
                  <i className="fas fa-sign-in-alt ml-2"></i>
                  تسجيل دخول الطلاب
                </a>
              </li>
              <li>
                <a href="#courses" className="text-gray-300 hover:text-gold transition-colors">
                  <i className="fas fa-book ml-2"></i>
                  الدورات التدريبية
                </a>
              </li>
              <li>
                <a href="#certificates" className="text-gray-300 hover:text-gold transition-colors">
                  <i className="fas fa-certificate ml-2"></i>
                  الشهادات
                </a>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h4 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <i className="fas fa-envelope text-gold"></i>
              تواصل معنا
            </h4>
            <div className="space-y-3 text-sm">
              <div className="flex items-center gap-3 text-gray-300">
                <i className="fas fa-user text-gold w-4"></i>
                <span>الأستاذ علاء عبد الحميد</span>
              </div>
              <div className="flex items-center gap-3 text-gray-300">
                <i className="fas fa-envelope text-gold w-4"></i>
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-3 text-gray-300">
                <i className="fas fa-phone text-gold w-4"></i>
                <span>+20 ************</span>
              </div>
              <div className="flex items-center gap-3 text-gray-300">
                <i className="fas fa-globe text-gold w-4"></i>
                <span>www.alaa-elearning.com</span>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 mt-8 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-sm text-gray-300">
              © {currentYear} منصة علاء عبد الحميد التعليمية. جميع الحقوق محفوظة.
            </div>
            
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-300">تابعنا على:</span>
              <div className="flex gap-3">
                <a 
                  href="#" 
                  className="w-8 h-8 bg-primary-blue rounded-full flex items-center justify-center hover:bg-secondary-blue transition-colors"
                >
                  <i className="fab fa-facebook-f text-white text-sm"></i>
                </a>
                <a 
                  href="#" 
                  className="w-8 h-8 bg-primary-blue rounded-full flex items-center justify-center hover:bg-secondary-blue transition-colors"
                >
                  <i className="fab fa-twitter text-white text-sm"></i>
                </a>
                <a 
                  href="#" 
                  className="w-8 h-8 bg-primary-blue rounded-full flex items-center justify-center hover:bg-secondary-blue transition-colors"
                >
                  <i className="fab fa-linkedin-in text-white text-sm"></i>
                </a>
                <a 
                  href="#" 
                  className="w-8 h-8 bg-primary-blue rounded-full flex items-center justify-center hover:bg-secondary-blue transition-colors"
                >
                  <i className="fab fa-youtube text-white text-sm"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
