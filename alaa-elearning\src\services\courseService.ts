import { supabase, TABLES, handleSupabaseError } from './supabase';
import { storage } from './firebase';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { Course, CourseSection, Video, PDFFile } from '../types';

// Course CRUD Operations
export const createCourse = async (courseData: Omit<Course, 'id' | 'createdAt' | 'updatedAt' | 'sections'>): Promise<Course> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.COURSES)
      .insert([{
        title: courseData.title,
        description: courseData.description,
        level: courseData.level,
        is_active: courseData.isActive,
        created_by: courseData.createdBy
      }])
      .select()
      .single();

    if (error) throw new Error(handleSupabaseError(error));

    return {
      id: data.id,
      title: data.title,
      description: data.description,
      level: data.level,
      sections: [],
      isActive: data.is_active,
      createdBy: data.created_by,
      createdAt: new Date(data.created_at),
      updatedAt: new Date(data.updated_at)
    };
  } catch (error: any) {
    console.error('Error creating course:', error);
    throw new Error(error.message || 'فشل في إنشاء الدورة');
  }
};

export const getCourses = async (): Promise<Course[]> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.COURSES)
      .select(`
        *,
        course_sections (
          *,
          videos (*),
          pdf_files (*)
        )
      `)
      .order('created_at', { ascending: false });

    if (error) throw new Error(handleSupabaseError(error));

    return data.map(course => ({
      id: course.id,
      title: course.title,
      description: course.description,
      level: course.level,
      sections: course.course_sections?.map((section: any) => ({
        id: section.id,
        courseId: section.course_id,
        title: section.title,
        description: section.description,
        order: section.order_index,
        videos: section.videos?.map((video: any) => ({
          id: video.id,
          sectionId: video.section_id,
          title: video.title,
          description: video.description,
          url: video.url,
          duration: video.duration,
          order: video.order_index,
          isActive: video.is_active,
          createdAt: new Date(video.created_at)
        })) || [],
        pdfs: section.pdf_files?.map((pdf: any) => ({
          id: pdf.id,
          sectionId: pdf.section_id,
          title: pdf.title,
          description: pdf.description,
          url: pdf.url,
          fileName: pdf.file_name,
          fileSize: pdf.file_size,
          order: pdf.order_index,
          isActive: pdf.is_active,
          createdAt: new Date(pdf.created_at)
        })) || [],
        isActive: section.is_active,
        createdAt: new Date(section.created_at)
      })) || [],
      isActive: course.is_active,
      createdBy: course.created_by,
      createdAt: new Date(course.created_at),
      updatedAt: new Date(course.updated_at)
    }));
  } catch (error: any) {
    console.error('Error fetching courses:', error);
    throw new Error(error.message || 'فشل في جلب الدورات');
  }
};

export const updateCourse = async (courseId: string, updates: Partial<Course>): Promise<void> => {
  try {
    const { error } = await supabase
      .from(TABLES.COURSES)
      .update({
        title: updates.title,
        description: updates.description,
        level: updates.level,
        is_active: updates.isActive,
        updated_at: new Date().toISOString()
      })
      .eq('id', courseId);

    if (error) throw new Error(handleSupabaseError(error));
  } catch (error: any) {
    console.error('Error updating course:', error);
    throw new Error(error.message || 'فشل في تحديث الدورة');
  }
};

// Course Section Operations
export const createSection = async (sectionData: Omit<CourseSection, 'id' | 'createdAt' | 'videos' | 'pdfs'>): Promise<CourseSection> => {
  try {
    const { data, error } = await supabase
      .from(TABLES.COURSE_SECTIONS)
      .insert([{
        course_id: sectionData.courseId,
        title: sectionData.title,
        description: sectionData.description,
        order_index: sectionData.order,
        is_active: sectionData.isActive
      }])
      .select()
      .single();

    if (error) throw new Error(handleSupabaseError(error));

    return {
      id: data.id,
      courseId: data.course_id,
      title: data.title,
      description: data.description,
      order: data.order_index,
      videos: [],
      pdfs: [],
      isActive: data.is_active,
      createdAt: new Date(data.created_at)
    };
  } catch (error: any) {
    console.error('Error creating section:', error);
    throw new Error(error.message || 'فشل في إنشاء القسم');
  }
};

// Video Upload and Management
export const uploadVideo = async (
  file: File,
  sectionId: string,
  title: string,
  description?: string
): Promise<Video> => {
  try {
    // Upload video to Firebase Storage
    const videoRef = ref(storage, `courses/videos/${sectionId}/${Date.now()}_${file.name}`);
    const snapshot = await uploadBytes(videoRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);

    // Get video duration (simplified - in real app, use video metadata)
    const duration = 0; // TODO: Extract actual video duration

    // Save video info to Supabase
    const { data, error } = await supabase
      .from(TABLES.VIDEOS)
      .insert([{
        section_id: sectionId,
        title,
        description,
        url: downloadURL,
        duration,
        order_index: 1, // TODO: Calculate proper order
        is_active: true
      }])
      .select()
      .single();

    if (error) throw new Error(handleSupabaseError(error));

    return {
      id: data.id,
      sectionId: data.section_id,
      title: data.title,
      description: data.description,
      url: data.url,
      duration: data.duration,
      order: data.order_index,
      isActive: data.is_active,
      createdAt: new Date(data.created_at)
    };
  } catch (error: any) {
    console.error('Error uploading video:', error);
    throw new Error(error.message || 'فشل في رفع الفيديو');
  }
};

// PDF Upload and Management
export const uploadPDF = async (
  file: File,
  sectionId: string,
  title: string,
  description?: string
): Promise<PDFFile> => {
  try {
    // Upload PDF to Firebase Storage
    const pdfRef = ref(storage, `courses/pdfs/${sectionId}/${Date.now()}_${file.name}`);
    const snapshot = await uploadBytes(pdfRef, file);
    const downloadURL = await getDownloadURL(snapshot.ref);

    // Save PDF info to Supabase
    const { data, error } = await supabase
      .from(TABLES.PDF_FILES)
      .insert([{
        section_id: sectionId,
        title,
        description,
        url: downloadURL,
        file_name: file.name,
        file_size: file.size,
        order_index: 1, // TODO: Calculate proper order
        is_active: true
      }])
      .select()
      .single();

    if (error) throw new Error(handleSupabaseError(error));

    return {
      id: data.id,
      sectionId: data.section_id,
      title: data.title,
      description: data.description,
      url: data.url,
      fileName: data.file_name,
      fileSize: data.file_size,
      order: data.order_index,
      isActive: data.is_active,
      createdAt: new Date(data.created_at)
    };
  } catch (error: any) {
    console.error('Error uploading PDF:', error);
    throw new Error(error.message || 'فشل في رفع ملف PDF');
  }
};

// Delete Operations
export const deleteVideo = async (videoId: string, videoUrl: string): Promise<void> => {
  try {
    // Delete from Firebase Storage
    const videoRef = ref(storage, videoUrl);
    await deleteObject(videoRef);

    // Delete from Supabase
    const { error } = await supabase
      .from(TABLES.VIDEOS)
      .delete()
      .eq('id', videoId);

    if (error) throw new Error(handleSupabaseError(error));
  } catch (error: any) {
    console.error('Error deleting video:', error);
    throw new Error(error.message || 'فشل في حذف الفيديو');
  }
};

export const deletePDF = async (pdfId: string, pdfUrl: string): Promise<void> => {
  try {
    // Delete from Firebase Storage
    const pdfRef = ref(storage, pdfUrl);
    await deleteObject(pdfRef);

    // Delete from Supabase
    const { error } = await supabase
      .from(TABLES.PDF_FILES)
      .delete()
      .eq('id', pdfId);

    if (error) throw new Error(handleSupabaseError(error));
  } catch (error: any) {
    console.error('Error deleting PDF:', error);
    throw new Error(error.message || 'فشل في حذف ملف PDF');
  }
};
