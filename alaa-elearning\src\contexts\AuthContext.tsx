import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, Admin } from '../types';
import { getCurrentUser, getCurrentAdmin } from '../services/auth';

interface AuthContextType {
  user: User | null;
  admin: Admin | null;
  loading: boolean;
  login: (accessCode: string) => Promise<User>;
  adminLogin: (email: string, password: string) => Promise<Admin>;
  logout: () => Promise<void>;
  adminLogout: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check for existing user session
        const currentUser = await getCurrentUser();
        if (currentUser) {
          setUser(currentUser);
        }

        // Check for existing admin session
        const currentAdmin = await getCurrentAdmin();
        if (currentAdmin) {
          setAdmin(currentAdmin);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (accessCode: string): Promise<User> => {
    try {
      setLoading(true);
      
      // Import auth service dynamically to avoid circular dependencies
      const { loginWithAccessCode } = await import('../services/auth');
      const loggedInUser = await loginWithAccessCode(accessCode);
      
      setUser(loggedInUser);
      return loggedInUser;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const adminLogin = async (email: string, password: string): Promise<Admin> => {
    try {
      setLoading(true);
      
      // Import auth service dynamically
      const { adminLoginWithEmail } = await import('../services/auth');
      const loggedInAdmin = await adminLoginWithEmail(email, password);
      
      setAdmin(loggedInAdmin);
      return loggedInAdmin;
    } catch (error) {
      console.error('Admin login error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setLoading(true);
      
      // Import auth service dynamically
      const { logoutUser } = await import('../services/auth');
      await logoutUser();
      
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const adminLogout = async (): Promise<void> => {
    try {
      setLoading(true);
      
      // Import auth service dynamically
      const { logoutAdmin } = await import('../services/auth');
      await logoutAdmin();
      
      setAdmin(null);
    } catch (error) {
      console.error('Admin logout error:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const refreshUser = async (): Promise<void> => {
    try {
      const currentUser = await getCurrentUser();
      setUser(currentUser);
    } catch (error) {
      console.error('Error refreshing user:', error);
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    admin,
    loading,
    login,
    adminLogin,
    logout,
    adminLogout,
    refreshUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
