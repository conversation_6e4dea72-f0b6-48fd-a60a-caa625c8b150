import { supabase, TABLES, handleSupabaseError } from './supabase';
import { auth } from './firebase';
import { 
  signInWithEmailAndPassword, 
  createUserWithEmailAndPassword,
  signOut,
  sendPasswordResetEmail,
  updatePassword,
  User as FirebaseUser
} from 'firebase/auth';
import { User, Admin, AccessCode } from '../types';

// Storage keys
const USER_STORAGE_KEY = 'alaa_elearning_user';
const ADMIN_STORAGE_KEY = 'alaa_elearning_admin';
const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours

// Session management
interface SessionData {
  user: User | Admin;
  timestamp: number;
  type: 'student' | 'admin';
}

// Utility functions
const isSessionValid = (sessionData: SessionData): boolean => {
  const now = Date.now();
  return (now - sessionData.timestamp) < SESSION_TIMEOUT;
};

const createSession = (user: User | Admin, type: 'student' | 'admin'): void => {
  const sessionData: SessionData = {
    user,
    timestamp: Date.now(),
    type
  };
  
  const storageKey = type === 'student' ? USER_STORAGE_KEY : ADMIN_STORAGE_KEY;
  localStorage.setItem(storageKey, JSON.stringify(sessionData));
};

const getSession = (type: 'student' | 'admin'): SessionData | null => {
  const storageKey = type === 'student' ? USER_STORAGE_KEY : ADMIN_STORAGE_KEY;
  const stored = localStorage.getItem(storageKey);
  
  if (!stored) return null;
  
  try {
    const sessionData: SessionData = JSON.parse(stored);
    return isSessionValid(sessionData) ? sessionData : null;
  } catch {
    return null;
  }
};

const clearSession = (type: 'student' | 'admin'): void => {
  const storageKey = type === 'student' ? USER_STORAGE_KEY : ADMIN_STORAGE_KEY;
  localStorage.removeItem(storageKey);
};

// Student Authentication
export const loginWithAccessCode = async (accessCode: string): Promise<User> => {
  try {
    // Validate access code format
    if (!/^\d{7}$/.test(accessCode)) {
      throw new Error('كود الوصول يجب أن يكون 7 أرقام');
    }

    // Check if access code exists and is valid
    const { data: accessCodeData, error: accessCodeError } = await supabase
      .from(TABLES.ACCESS_CODES)
      .select(`
        *,
        users (*)
      `)
      .eq('code', accessCode)
      .eq('is_used', true)
      .single();

    if (accessCodeError || !accessCodeData) {
      throw new Error('كود الوصول غير صحيح أو غير مفعل');
    }

    // Check if access code is expired
    if (accessCodeData.expires_at && new Date(accessCodeData.expires_at) < new Date()) {
      throw new Error('كود الوصول منتهي الصلاحية');
    }

    // Get user data
    const userData = accessCodeData.users;
    if (!userData || !userData.is_active) {
      throw new Error('الحساب غير مفعل');
    }

    const user: User = {
      id: userData.id,
      name: userData.name,
      email: userData.email,
      accessCode: userData.access_code,
      isActive: userData.is_active,
      enrolledCourses: userData.enrolled_courses || [],
      certificates: [],
      createdAt: new Date(userData.created_at),
      updatedAt: new Date(userData.updated_at)
    };

    // Create session
    createSession(user, 'student');

    // Update last login
    await supabase
      .from(TABLES.USERS)
      .update({ updated_at: new Date().toISOString() })
      .eq('id', user.id);

    return user;
  } catch (error: any) {
    console.error('Student login error:', error);
    throw new Error(error.message || 'حدث خطأ أثناء تسجيل الدخول');
  }
};

export const getCurrentUser = async (): Promise<User | null> => {
  try {
    const session = getSession('student');
    if (!session) return null;

    const user = session.user as User;
    
    // Verify user still exists and is active
    const { data: userData, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', user.id)
      .eq('is_active', true)
      .single();

    if (error || !userData) {
      clearSession('student');
      return null;
    }

    // Update session with fresh data
    const updatedUser: User = {
      id: userData.id,
      name: userData.name,
      email: userData.email,
      accessCode: userData.access_code,
      isActive: userData.is_active,
      enrolledCourses: userData.enrolled_courses || [],
      certificates: [],
      createdAt: new Date(userData.created_at),
      updatedAt: new Date(userData.updated_at)
    };

    createSession(updatedUser, 'student');
    return updatedUser;
  } catch (error) {
    console.error('Get current user error:', error);
    clearSession('student');
    return null;
  }
};

export const logoutUser = async (): Promise<void> => {
  clearSession('student');
};

// Admin Authentication
export const adminLoginWithEmail = async (email: string, password: string): Promise<Admin> => {
  try {
    // Demo admin credentials
    if (email === '<EMAIL>' && password === 'AlaaAdmin2024!') {
      const adminData: Admin = {
        id: 'admin-1',
        name: 'علاء عبد الحميد',
        email: '<EMAIL>',
        role: 'super_admin',
        createdAt: new Date()
      };

      createSession(adminData, 'admin');
      return adminData;
    }

    // Try Firebase authentication for other admins
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const firebaseUser = userCredential.user;

      // Get admin data from Supabase
      const { data: adminData, error } = await supabase
        .from(TABLES.ADMINS)
        .select('*')
        .eq('email', email)
        .single();

      if (error || !adminData) {
        await signOut(auth);
        throw new Error('المستخدم غير مخول للوصول لوحة الإدارة');
      }

      const admin: Admin = {
        id: adminData.id,
        name: adminData.name,
        email: adminData.email,
        role: adminData.role,
        createdAt: new Date(adminData.created_at)
      };

      createSession(admin, 'admin');
      return admin;
    } catch (firebaseError: any) {
      throw new Error('بيانات تسجيل الدخول غير صحيحة');
    }
  } catch (error: any) {
    console.error('Admin login error:', error);
    throw new Error(error.message || 'حدث خطأ أثناء تسجيل دخول المدير');
  }
};

export const getCurrentAdmin = async (): Promise<Admin | null> => {
  try {
    const session = getSession('admin');
    if (!session) return null;

    const admin = session.user as Admin;
    
    // If it's the demo admin, return it directly
    if (admin.email === '<EMAIL>' && admin.id === 'admin-1') {
      return admin;
    }
    
    // For other admins, verify they still exist in Supabase
    const { data: adminData, error } = await supabase
      .from(TABLES.ADMINS)
      .select('*')
      .eq('id', admin.id)
      .single();

    if (error || !adminData) {
      clearSession('admin');
      await signOut(auth);
      return null;
    }

    const updatedAdmin: Admin = {
      id: adminData.id,
      name: adminData.name,
      email: adminData.email,
      role: adminData.role,
      createdAt: new Date(adminData.created_at)
    };

    createSession(updatedAdmin, 'admin');
    return updatedAdmin;
  } catch (error) {
    console.error('Get current admin error:', error);
    clearSession('admin');
    return null;
  }
};

export const logoutAdmin = async (): Promise<void> => {
  clearSession('admin');
  try {
    await signOut(auth);
  } catch (error) {
    console.error('Firebase signout error:', error);
  }
};

// Admin Management Functions
export const createAdmin = async (
  name: string, 
  email: string, 
  password: string, 
  role: 'admin' | 'super_admin' = 'admin'
): Promise<Admin> => {
  try {
    // Create Firebase user
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const firebaseUser = userCredential.user;

    // Create admin record in Supabase
    const { data: adminData, error } = await supabase
      .from(TABLES.ADMINS)
      .insert([{
        name,
        email,
        role
      }])
      .select()
      .single();

    if (error) {
      // If Supabase fails, delete Firebase user
      await firebaseUser.delete();
      throw new Error(handleSupabaseError(error));
    }

    return {
      id: adminData.id,
      name: adminData.name,
      email: adminData.email,
      role: adminData.role,
      createdAt: new Date(adminData.created_at)
    };
  } catch (error: any) {
    console.error('Create admin error:', error);
    throw new Error(error.message || 'فشل في إنشاء حساب المدير');
  }
};

export const changeAdminPassword = async (currentPassword: string, newPassword: string): Promise<void> => {
  try {
    const user = auth.currentUser;
    if (!user) {
      throw new Error('لم يتم العثور على المستخدم');
    }

    // Re-authenticate with current password
    await signInWithEmailAndPassword(auth, user.email!, currentPassword);
    
    // Update password
    await updatePassword(user, newPassword);
  } catch (error: any) {
    console.error('Change password error:', error);
    throw new Error(error.message || 'فشل في تغيير كلمة المرور');
  }
};

export const resetPassword = async (email: string): Promise<void> => {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error: any) {
    console.error('Reset password error:', error);
    throw new Error(error.message || 'فشل في إرسال رابط إعادة تعيين كلمة المرور');
  }
};

// Session utilities
export const checkAdminAuth = async (): Promise<boolean> => {
  const admin = await getCurrentAdmin();
  return !!admin;
};

export const checkUserAuth = async (): Promise<boolean> => {
  const user = await getCurrentUser();
  return !!user;
};

export const refreshSession = async (type: 'student' | 'admin'): Promise<void> => {
  if (type === 'student') {
    await getCurrentUser();
  } else {
    await getCurrentAdmin();
  }
};

// Security utilities
export const validateAccessCode = (code: string): boolean => {
  return /^\d{7}$/.test(code);
};

export const generateSecureAccessCode = (): string => {
  return Math.floor(1000000 + Math.random() * 9000000).toString();
};

export const logSecurityEvent = async (
  event: string, 
  userId?: string, 
  details?: any
): Promise<void> => {
  try {
    // Log security events for audit trail
    console.log('Security Event:', { event, userId, details, timestamp: new Date() });
    
    // TODO: Implement actual security logging to database
  } catch (error) {
    console.error('Security logging error:', error);
  }
};
