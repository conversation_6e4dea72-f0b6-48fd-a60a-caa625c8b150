import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { User } from '../types';
import { useAuth } from '../contexts/AuthContext';

interface HeaderProps {
  user?: User | null;
  isAdmin?: boolean;
}

const Header: React.FC<HeaderProps> = ({ user, isAdmin }) => {
  const navigate = useNavigate();
  const { logout, adminLogout } = useAuth();

  const handleLogout = async () => {
    try {
      if (isAdmin) {
        await adminLogout();
        navigate('/');
      } else {
        await logout();
        navigate('/');
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  return (
    <header className="bg-white shadow-md border-b border-border-color">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Title */}
          <Link to="/" className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary-blue rounded-lg flex items-center justify-center">
              <i className="fas fa-graduation-cap text-white text-lg"></i>
            </div>
            <div>
              <h1 className="text-xl font-bold text-text-dark">
                منصة علاء عبد الحميد
              </h1>
              <p className="text-sm text-text-light">
                التعليمية
              </p>
            </div>
          </Link>

          {/* Navigation */}
          <nav className="hidden md:flex items-center gap-6">
            <Link 
              to="/" 
              className="text-text-light hover:text-primary-blue transition-colors"
            >
              الرئيسية
            </Link>
            
            {user && (
              <Link 
                to="/dashboard" 
                className="text-text-light hover:text-primary-blue transition-colors"
              >
                لوحة الطالب
              </Link>
            )}
            
            {isAdmin && (
              <Link 
                to={`/${import.meta.env.VITE_ADMIN_SECRET_PATH || 'admin'}/dashboard`}
                className="text-text-light hover:text-primary-blue transition-colors"
              >
                لوحة الإدارة
              </Link>
            )}
          </nav>

          {/* User Actions */}
          <div className="flex items-center gap-4">
            {user ? (
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-text-dark">
                    {user.name}
                  </p>
                  <p className="text-xs text-text-light">
                    طالب
                  </p>
                </div>
                <button
                  onClick={handleLogout}
                  className="btn btn-outline text-sm"
                >
                  <i className="fas fa-sign-out-alt"></i>
                  تسجيل خروج
                </button>
              </div>
            ) : isAdmin ? (
              <div className="flex items-center gap-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-text-dark">
                    مدير النظام
                  </p>
                  <p className="text-xs text-text-light">
                    إدارة
                  </p>
                </div>
                <button
                  onClick={handleLogout}
                  className="btn btn-outline text-sm"
                >
                  <i className="fas fa-sign-out-alt"></i>
                  تسجيل خروج
                </button>
              </div>
            ) : (
              <Link to="/login" className="btn btn-primary">
                <i className="fas fa-sign-in-alt"></i>
                تسجيل دخول
              </Link>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button className="md:hidden p-2 text-text-light hover:text-primary-blue">
            <i className="fas fa-bars text-lg"></i>
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
