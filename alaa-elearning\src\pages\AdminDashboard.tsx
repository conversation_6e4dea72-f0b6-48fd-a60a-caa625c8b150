import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';

// Admin Components
import AdminSidebar from '../components/admin/AdminSidebar';
import DashboardOverview from '../components/admin/DashboardOverview';
import CourseManagement from '../components/admin/CourseManagement';
import StudentManagement from '../components/admin/StudentManagement';
import AccessCodeManagement from '../components/admin/AccessCodeManagement';
import CertificateManagement from '../components/admin/CertificateManagement';
import QuizManagement from '../components/admin/QuizManagement';
import SettingsManagement from '../components/admin/SettingsManagement';

type AdminView = 'overview' | 'courses' | 'students' | 'access-codes' | 'certificates' | 'quizzes' | 'settings';

const AdminDashboard: React.FC = () => {
  const { admin, loading } = useAuth();
  const [currentView, setCurrentView] = useState<AdminView>('overview');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  useEffect(() => {
    // Set page title
    document.title = 'لوحة الإدارة - منصة علاء عبد الحميد التعليمية';
  }, []);

  if (loading) {
    return <LoadingSpinner text="جاري تحميل لوحة الإدارة..." />;
  }

  if (!admin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="text-center">
          <i className="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
          <h2 className="text-2xl font-bold text-red-700 mb-2">غير مخول للوصول</h2>
          <p className="text-red-600">ليس لديك صلاحية للوصول لهذه الصفحة</p>
        </div>
      </div>
    );
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'overview':
        return <DashboardOverview />;
      case 'courses':
        return <CourseManagement />;
      case 'students':
        return <StudentManagement />;
      case 'access-codes':
        return <AccessCodeManagement />;
      case 'certificates':
        return <CertificateManagement />;
      case 'quizzes':
        return <QuizManagement />;
      case 'settings':
        return <SettingsManagement />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <AdminSidebar
        currentView={currentView}
        setCurrentView={setCurrentView}
        isOpen={sidebarOpen}
        setIsOpen={setSidebarOpen}
        admin={admin}
      />

      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'mr-64' : 'mr-16'}`}>
        {/* Top Bar */}
        <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <i className="fas fa-bars text-gray-600"></i>
              </button>
              <h1 className="text-2xl font-bold text-gray-800">
                لوحة الإدارة
              </h1>
            </div>

            <div className="flex items-center gap-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-700">
                  {admin.name}
                </p>
                <p className="text-xs text-gray-500">
                  {admin.role === 'super_admin' ? 'مدير عام' : 'مدير'}
                </p>
              </div>
              <div className="w-10 h-10 bg-primary-blue rounded-full flex items-center justify-center">
                <i className="fas fa-user-shield text-white"></i>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="p-6">
          {renderCurrentView()}
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;
