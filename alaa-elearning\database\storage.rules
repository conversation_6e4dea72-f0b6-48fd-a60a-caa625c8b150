// Firebase Storage Security Rules for Alaa E-Learning Platform
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             firestore.exists(/databases/(default)/documents/admins/$(request.auth.uid));
    }
    
    function isActiveUser() {
      return isAuthenticated() && 
             firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.isActive == true;
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*');
    }
    
    function isValidVideoFile() {
      return request.resource.contentType.matches('video/.*');
    }
    
    function isValidPDFFile() {
      return request.resource.contentType == 'application/pdf';
    }
    
    function isValidFileSize(maxSizeInMB) {
      return request.resource.size < maxSizeInMB * 1024 * 1024;
    }
    
    // Course videos (admin upload only)
    match /courses/{courseId}/videos/{videoId} {
      allow read: if isActiveUser() || isAdmin();
      allow write: if isAdmin() && isValidVideoFile() && isValidFileSize(500); // 500MB max
    }
    
    // Course PDFs (admin upload only)
    match /courses/{courseId}/pdfs/{pdfId} {
      allow read: if isActiveUser() || isAdmin();
      allow write: if isAdmin() && isValidPDFFile() && isValidFileSize(50); // 50MB max
    }
    
    // Certificate templates (admin upload only)
    match /certificate-templates/{templateId} {
      allow read: if isActiveUser() || isAdmin();
      allow write: if isAdmin() && isValidImageFile() && isValidFileSize(10); // 10MB max
    }
    
    // Generated certificates (admin generate, users can read their own)
    match /certificates/{userId}/{certificateId} {
      allow read: if request.auth.uid == userId || isAdmin();
      allow write: if isAdmin();
    }
    
    // User profile images (users can upload their own)
    match /users/{userId}/profile/{imageId} {
      allow read: if request.auth.uid == userId || isAdmin();
      allow write: if (request.auth.uid == userId || isAdmin()) && 
                      isValidImageFile() && isValidFileSize(5); // 5MB max
    }
    
    // Admin uploads (admin only)
    match /admin/{allPaths=**} {
      allow read, write: if isAdmin();
    }
    
    // Temporary uploads (for processing)
    match /temp/{userId}/{allPaths=**} {
      allow read, write: if request.auth.uid == userId || isAdmin();
      // Auto-delete after 24 hours (handled by Cloud Functions)
    }
    
    // Public assets (read-only for all)
    match /public/{allPaths=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Default deny rule
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
