import React, { useState } from 'react';
import LoadingSpinner from '../LoadingSpinner';

const CertificateManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الشهادات</h1>
          <p className="text-gray-600">إنشاء قوالب الشهادات وإصدارها للطلاب</p>
        </div>
        <button className="btn btn-primary">
          <i className="fas fa-plus"></i>
          إضافة قالب جديد
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="text-center">
            <i className="fas fa-certificate text-4xl text-gold mb-4"></i>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">قوالب الشهادات</h3>
            <p className="text-gray-600 mb-4">إدارة قوالب الشهادات المختلفة</p>
            <button className="btn btn-outline">إدارة القوالب</button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="text-center">
            <i className="fas fa-award text-4xl text-primary-blue mb-4"></i>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">إصدار شهادات</h3>
            <p className="text-gray-600 mb-4">إصدار شهادات للطلاب المتميزين</p>
            <button className="btn btn-outline">إصدار شهادة</button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="text-center">
            <i className="fas fa-history text-4xl text-green-500 mb-4"></i>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">سجل الشهادات</h3>
            <p className="text-gray-600 mb-4">عرض جميع الشهادات المصدرة</p>
            <button className="btn btn-outline">عرض السجل</button>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-8 text-center">
        <i className="fas fa-tools text-4xl text-gray-400 mb-4"></i>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">قيد التطوير</h3>
        <p className="text-gray-500">نظام إدارة الشهادات سيكون متاحاً قريباً</p>
      </div>
    </div>
  );
};

export default CertificateManagement;
