<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد المدير - منصة علاء عبد الحميد</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
        }
        .step {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .step h3 {
            color: #1e40af;
            margin-top: 0;
        }
        .step-number {
            background: #ffd700;
            color: #1f2937;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 10px;
        }
        .link {
            background: #2563eb;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 0;
        }
        .link:hover {
            background: #1d4ed8;
        }
        .code {
            background: #1f2937;
            color: #10b981;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .warning {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        .success {
            background: #d1fae5;
            border: 2px solid #10b981;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 إعداد لوحة الإدارة</h1>
        <p style="text-align: center; color: #6b7280;">منصة علاء عبد الحميد التعليمية</p>

        <div class="warning">
            <strong>⚠️ مهم:</strong> يجب إكمال هذه الخطوات لتفعيل لوحة الإدارة بشكل كامل
        </div>

        <div class="step">
            <h3><span class="step-number">1</span>تفعيل Firebase Authentication</h3>
            <p>قم بزيارة Firebase Console وتفعيل خدمة المصادقة:</p>
            <a href="https://console.firebase.google.com/project/marketwise-academy-qhizq/authentication" 
               target="_blank" class="link">
                🔥 فتح Firebase Authentication
            </a>
            <ul>
                <li>انقر على "Get started"</li>
                <li>اذهب إلى "Sign-in method"</li>
                <li>فعل "Email/Password"</li>
                <li>احفظ الإعدادات</li>
            </ul>
        </div>

        <div class="step">
            <h3><span class="step-number">2</span>إنشاء حساب المدير</h3>
            <p>في نفس صفحة Authentication:</p>
            <ul>
                <li>اذهب إلى تبويب "Users"</li>
                <li>انقر على "Add user"</li>
                <li>أدخل البيانات التالية:</li>
            </ul>
            <div class="code">
البريد الإلكتروني: <EMAIL>
كلمة المرور: AlaaAdmin2024!
            </div>
        </div>

        <div class="step">
            <h3><span class="step-number">3</span>تفعيل Firestore Database</h3>
            <a href="https://console.firebase.google.com/project/marketwise-academy-qhizq/firestore" 
               target="_blank" class="link">
                🗄️ فتح Firestore Database
            </a>
            <ul>
                <li>انقر على "Create database"</li>
                <li>اختر "Start in production mode"</li>
                <li>اختر المنطقة الأقرب</li>
                <li>انقر على "Done"</li>
            </ul>
        </div>

        <div class="step">
            <h3><span class="step-number">4</span>تفعيل Firebase Storage</h3>
            <a href="https://console.firebase.google.com/project/marketwise-academy-qhizq/storage" 
               target="_blank" class="link">
                📁 فتح Firebase Storage
            </a>
            <ul>
                <li>انقر على "Get started"</li>
                <li>اختر "Start in production mode"</li>
                <li>اختر نفس المنطقة المختارة في Firestore</li>
                <li>انقر على "Done"</li>
            </ul>
        </div>

        <div class="step">
            <h3><span class="step-number">5</span>الحصول على إعدادات Firebase</h3>
            <a href="https://console.firebase.google.com/project/marketwise-academy-qhizq/settings/general" 
               target="_blank" class="link">
                ⚙️ فتح إعدادات المشروع
            </a>
            <ul>
                <li>انزل إلى قسم "Your apps"</li>
                <li>إذا لم يوجد تطبيق ويب، انقر على "Add app" واختر "Web"</li>
                <li>انسخ كائن التكوين وحدث ملف .env</li>
            </ul>
        </div>

        <div class="success">
            <h3>🎉 بعد إكمال الإعداد:</h3>
            <p><strong>رابط لوحة الإدارة:</strong></p>
            <div class="code">
https://marketwise-academy-qhizq.web.app/alaa-admin-2024
            </div>
            <p><strong>بيانات تسجيل الدخول:</strong></p>
            <div class="code">
البريد الإلكتروني: <EMAIL>
كلمة المرور: AlaaAdmin2024!
            </div>
        </div>

        <div class="step">
            <h3><span class="step-number">6</span>اختبار النظام</h3>
            <p>بعد إكمال جميع الخطوات:</p>
            <a href="https://marketwise-academy-qhizq.web.app/alaa-admin-2024" 
               target="_blank" class="link">
                🚀 اختبار لوحة الإدارة
            </a>
        </div>

        <div class="warning">
            <strong>🔒 ملاحظة أمنية:</strong> 
            <br>• مسار لوحة الإدارة مخفي ولا يظهر في أي مكان بالموقع
            <br>• يمكن تغيير المسار من متغير VITE_ADMIN_SECRET_PATH
            <br>• تأكد من استخدام كلمة مرور قوية للمدير
        </div>
    </div>
</body>
</html>
