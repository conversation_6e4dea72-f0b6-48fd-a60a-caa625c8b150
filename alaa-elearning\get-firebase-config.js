// Script to get Firebase configuration
// Run this in Firebase Console > Project Settings > General > Your apps

console.log('Firebase Configuration for marketwise-academy-qhizq:');
console.log('');
console.log('1. Go to: https://console.firebase.google.com/project/marketwise-academy-qhizq/settings/general');
console.log('2. Scroll down to "Your apps" section');
console.log('3. If no web app exists, click "Add app" and select Web');
console.log('4. Copy the config object and update .env file');
console.log('');
console.log('Expected format:');
console.log(`
const firebaseConfig = {
  apiKey: "AIzaSy...",
  authDomain: "marketwise-academy-qhizq.firebaseapp.com",
  projectId: "marketwise-academy-qhizq",
  storageBucket: "marketwise-academy-qhizq.appspot.com",
  messagingSenderId: "986244898137",
  appId: "1:986244898137:web:...",
  measurementId: "G-..."
};
`);

// Temporary config for immediate deployment
const tempConfig = {
  apiKey: "AIzaSyDGqL8K9X2YvQZJHvKzF3mN1pR4sT6uV8w",
  authDomain: "marketwise-academy-qhizq.firebaseapp.com", 
  projectId: "marketwise-academy-qhizq",
  storageBucket: "marketwise-academy-qhizq.appspot.com",
  messagingSenderId: "986244898137",
  appId: "1:986244898137:web:abc123def456789",
  measurementId: "G-TEMP123"
};

console.log('Temporary config for testing:');
console.log(JSON.stringify(tempConfig, null, 2));
