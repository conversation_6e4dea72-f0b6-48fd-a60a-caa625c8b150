#!/bin/bash

# Script to deploy security rules after Firebase services are enabled
# Run this after completing Firebase setup

echo "🔥 نشر قواعد الأمان لمنصة علاء عبد الحميد التعليمية"
echo "=================================================="

echo "📋 نشر قواعد Firestore..."
firebase deploy --only firestore:rules
if [ $? -eq 0 ]; then
    echo "✅ تم نشر قواعد Firestore بنجاح"
else
    echo "❌ فشل في نشر قواعد Firestore"
fi

echo ""
echo "📁 نشر قواعد Storage..."
firebase deploy --only storage
if [ $? -eq 0 ]; then
    echo "✅ تم نشر قواعد Storage بنجاح"
else
    echo "❌ فشل في نشر قواعد Storage - تأكد من تفعيل Firebase Storage أولاً"
fi

echo ""
echo "📊 نشر فهارس Firestore..."
firebase deploy --only firestore:indexes
if [ $? -eq 0 ]; then
    echo "✅ تم نشر فهارس Firestore بنجاح"
else
    echo "❌ فشل في نشر فهارس Firestore"
fi

echo ""
echo "🎉 انتهى نشر قواعد الأمان!"
echo ""
echo "🔗 روابط مهمة:"
echo "• لوحة الإدارة: https://marketwise-academy-qhizq.web.app/alaa-admin-2024"
echo "• Firebase Console: https://console.firebase.google.com/project/marketwise-academy-qhizq"
echo ""
echo "👤 بيانات المدير:"
echo "• البريد الإلكتروني: <EMAIL>"
echo "• كلمة المرور: AlaaAdmin2024!"
