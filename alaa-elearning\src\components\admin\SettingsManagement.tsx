import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import PasswordManager from '../auth/PasswordManager';
import SecurityAudit from '../auth/SecurityAudit';

const SettingsManagement: React.FC = () => {
  const { admin } = useAuth();
  const [showPasswordManager, setShowPasswordManager] = useState(false);
  const [showSecurityAudit, setShowSecurityAudit] = useState(false);
  const [settings, setSettings] = useState({
    siteName: 'منصة علاء عبد الحميد التعليمية',
    adminEmail: '<EMAIL>',
    maxStudentsPerCourse: 100,
    certificateAutoIssue: true,
    emailNotifications: true,
    maintenanceMode: false
  });

  const handleSaveSettings = () => {
    // TODO: Save settings to database
    console.log('Settings saved:', settings);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إعدادات النظام</h1>
          <p className="text-gray-600">إدارة إعدادات المنصة العامة</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">الإعدادات العامة</h3>
          </div>
          <div className="p-6 space-y-4">
            <div>
              <label className="form-label">اسم المنصة</label>
              <input
                type="text"
                value={settings.siteName}
                onChange={(e) => setSettings({...settings, siteName: e.target.value})}
                className="form-input"
              />
            </div>
            
            <div>
              <label className="form-label">بريد المدير الإلكتروني</label>
              <input
                type="email"
                value={settings.adminEmail}
                onChange={(e) => setSettings({...settings, adminEmail: e.target.value})}
                className="form-input"
              />
            </div>
            
            <div>
              <label className="form-label">الحد الأقصى للطلاب في الدورة</label>
              <input
                type="number"
                value={settings.maxStudentsPerCourse}
                onChange={(e) => setSettings({...settings, maxStudentsPerCourse: parseInt(e.target.value)})}
                className="form-input"
              />
            </div>
          </div>
        </div>

        {/* Feature Settings */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">إعدادات المميزات</h3>
          </div>
          <div className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="font-medium text-gray-900">إصدار الشهادات تلقائياً</label>
                <p className="text-sm text-gray-500">إصدار شهادة عند إكمال الدورة</p>
              </div>
              <input
                type="checkbox"
                checked={settings.certificateAutoIssue}
                onChange={(e) => setSettings({...settings, certificateAutoIssue: e.target.checked})}
                className="w-4 h-4"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="font-medium text-gray-900">إشعارات البريد الإلكتروني</label>
                <p className="text-sm text-gray-500">إرسال إشعارات للطلاب والمديرين</p>
              </div>
              <input
                type="checkbox"
                checked={settings.emailNotifications}
                onChange={(e) => setSettings({...settings, emailNotifications: e.target.checked})}
                className="w-4 h-4"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="font-medium text-gray-900">وضع الصيانة</label>
                <p className="text-sm text-gray-500">إغلاق المنصة مؤقتاً للصيانة</p>
              </div>
              <input
                type="checkbox"
                checked={settings.maintenanceMode}
                onChange={(e) => setSettings({...settings, maintenanceMode: e.target.checked})}
                className="w-4 h-4"
              />
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">إعدادات الأمان</h3>
          </div>
          <div className="p-6 space-y-4">
            <button
              onClick={() => setShowPasswordManager(true)}
              className="btn btn-outline w-full"
            >
              <i className="fas fa-key"></i>
              تغيير كلمة مرور المدير
            </button>

            <button
              onClick={() => setShowSecurityAudit(true)}
              className="btn btn-outline w-full"
            >
              <i className="fas fa-shield-alt"></i>
              تدقيق الأمان
            </button>
            
            <button className="btn btn-outline w-full">
              <i className="fas fa-download"></i>
              تصدير نسخة احتياطية
            </button>
          </div>
        </div>

        {/* System Info */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">معلومات النظام</h3>
          </div>
          <div className="p-6 space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">إصدار المنصة:</span>
              <span className="font-medium">1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">آخر تحديث:</span>
              <span className="font-medium">2024-07-14</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">حالة قاعدة البيانات:</span>
              <span className="text-green-600 font-medium">متصلة</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">مساحة التخزين:</span>
              <span className="font-medium">2.5 GB / 10 GB</span>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSaveSettings}
          className="btn btn-primary"
        >
          <i className="fas fa-save"></i>
          حفظ الإعدادات
        </button>
      </div>

      {/* Password Manager Modal */}
      <PasswordManager
        isOpen={showPasswordManager}
        onClose={() => setShowPasswordManager(false)}
        userEmail={admin?.email}
      />

      {/* Security Audit Modal */}
      <SecurityAudit
        isOpen={showSecurityAudit}
        onClose={() => setShowSecurityAudit(false)}
      />
    </div>
  );
};

export default SettingsManagement;
