{"hosting": {"public": "dist", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}]}, "firestore": {"rules": "database/firestore.rules", "indexes": "database/firestore.indexes.json"}, "storage": {"rules": "database/storage.rules"}, "emulators": {"auth": {"port": 9099}, "firestore": {"port": 8080}, "storage": {"port": 9199}, "functions": {"port": 5001}, "hosting": {"port": 5000}, "ui": {"enabled": true, "port": 4000}}}