{"indexes": [{"collectionGroup": "courses", "queryScope": "COLLECTION", "fields": [{"fieldPath": "level", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "userProgress", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "courseId", "order": "ASCENDING"}, {"fieldPath": "completed", "order": "ASCENDING"}]}, {"collectionGroup": "quizAttempts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "quizId", "order": "ASCENDING"}, {"fieldPath": "startedAt", "order": "DESCENDING"}]}, {"collectionGroup": "certificates", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "issueDate", "order": "DESCENDING"}]}, {"collectionGroup": "accessCodes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isUsed", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}