{"version": 3, "sources": ["../../@firebase/functions/src/serializer.ts", "../../@firebase/functions/src/constants.ts", "../../@firebase/functions/src/error.ts", "../../@firebase/functions/src/context.ts", "../../@firebase/functions/src/service.ts", "../../@firebase/functions/src/config.ts", "../../@firebase/functions/src/api.ts", "../../@firebase/functions/src/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nconst LONG_TYPE = 'type.googleapis.com/google.protobuf.Int64Value';\nconst UNSIGNED_LONG_TYPE = 'type.googleapis.com/google.protobuf.UInt64Value';\n\nfunction mapValues(\n  // { [k: string]: unknown } is no longer a wildcard assignment target after typescript 3.5\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  o: { [key: string]: any },\n  f: (arg0: unknown) => unknown\n): object {\n  const result: { [key: string]: unknown } = {};\n  for (const key in o) {\n    if (o.hasOwnProperty(key)) {\n      result[key] = f(o[key]);\n    }\n  }\n  return result;\n}\n\n/**\n * Takes data and encodes it in a JSON-friendly way, such that types such as\n * Date are preserved.\n * @internal\n * @param data - Data to encode.\n */\nexport function encode(data: unknown): unknown {\n  if (data == null) {\n    return null;\n  }\n  if (data instanceof Number) {\n    data = data.valueOf();\n  }\n  if (typeof data === 'number' && isFinite(data)) {\n    // Any number in JS is safe to put directly in JSON and parse as a double\n    // without any loss of precision.\n    return data;\n  }\n  if (data === true || data === false) {\n    return data;\n  }\n  if (Object.prototype.toString.call(data) === '[object String]') {\n    return data;\n  }\n  if (data instanceof Date) {\n    return data.toISOString();\n  }\n  if (Array.isArray(data)) {\n    return data.map(x => encode(x));\n  }\n  if (typeof data === 'function' || typeof data === 'object') {\n    return mapValues(data!, x => encode(x));\n  }\n  // If we got this far, the data is not encodable.\n  throw new Error('Data cannot be encoded in JSON: ' + data);\n}\n\n/**\n * Takes data that's been encoded in a JSON-friendly form and returns a form\n * with richer datatypes, such as Dates, etc.\n * @internal\n * @param json - JSON to convert.\n */\nexport function decode(json: unknown): unknown {\n  if (json == null) {\n    return json;\n  }\n  if ((json as { [key: string]: unknown })['@type']) {\n    switch ((json as { [key: string]: unknown })['@type']) {\n      case LONG_TYPE:\n      // Fall through and handle this the same as unsigned.\n      case UNSIGNED_LONG_TYPE: {\n        // Technically, this could work return a valid number for malformed\n        // data if there was a number followed by garbage. But it's just not\n        // worth all the extra code to detect that case.\n        const value = Number((json as { [key: string]: unknown })['value']);\n        if (isNaN(value)) {\n          throw new Error('Data cannot be decoded from JSON: ' + json);\n        }\n        return value;\n      }\n      default: {\n        throw new Error('Data cannot be decoded from JSON: ' + json);\n      }\n    }\n  }\n  if (Array.isArray(json)) {\n    return json.map(x => decode(x));\n  }\n  if (typeof json === 'function' || typeof json === 'object') {\n    return mapValues(json!, x => decode(x));\n  }\n  // Anything else is safe to return.\n  return json;\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * Type constant for Firebase Functions.\n */\nexport const FUNCTIONS_TYPE = 'functions';\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FunctionsErrorCodeCore as FunctionsErrorCode } from './public-types';\nimport { decode } from './serializer';\nimport { HttpResponseBody } from './service';\nimport { FirebaseError } from '@firebase/util';\nimport { FUNCTIONS_TYPE } from './constants';\n\n/**\n * Standard error codes for different ways a request can fail, as defined by:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * This map is used primarily to convert from a backend error code string to\n * a client SDK error code string, and make sure it's in the supported set.\n */\nconst errorCodeMap: { [name: string]: FunctionsErrorCode } = {\n  OK: 'ok',\n  CANCELLED: 'cancelled',\n  UNKNOWN: 'unknown',\n  INVALID_ARGUMENT: 'invalid-argument',\n  DEADLINE_EXCEEDED: 'deadline-exceeded',\n  NOT_FOUND: 'not-found',\n  ALREADY_EXISTS: 'already-exists',\n  PERMISSION_DENIED: 'permission-denied',\n  UNAUTHENTICATED: 'unauthenticated',\n  RESOURCE_EXHAUSTED: 'resource-exhausted',\n  FAILED_PRECONDITION: 'failed-precondition',\n  ABORTED: 'aborted',\n  OUT_OF_RANGE: 'out-of-range',\n  UNIMPLEMENTED: 'unimplemented',\n  INTERNAL: 'internal',\n  UNAVAILABLE: 'unavailable',\n  DATA_LOSS: 'data-loss'\n};\n\n/**\n * An error returned by the Firebase Functions client SDK.\n *\n * See {@link FunctionsErrorCode} for full documentation of codes.\n *\n * @public\n */\nexport class FunctionsError extends FirebaseError {\n  /**\n   * Constructs a new instance of the `FunctionsError` class.\n   */\n  constructor(\n    /**\n     * A standard error code that will be returned to the client. This also\n     * determines the HTTP status code of the response, as defined in code.proto.\n     */\n    code: FunctionsErrorCode,\n    message?: string,\n    /**\n     * Additional details to be converted to JSON and included in the error response.\n     */\n    readonly details?: unknown\n  ) {\n    super(`${FUNCTIONS_TYPE}/${code}`, message || '');\n\n    // Since the FirebaseError constructor sets the prototype of `this` to FirebaseError.prototype,\n    // we also have to do it in all subclasses to allow for correct `instanceof` checks.\n    Object.setPrototypeOf(this, FunctionsError.prototype);\n  }\n}\n\n/**\n * Takes an HTTP status code and returns the corresponding ErrorCode.\n * This is the standard HTTP status code -> error mapping defined in:\n * https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto\n *\n * @param status An HTTP status code.\n * @return The corresponding ErrorCode, or ErrorCode.UNKNOWN if none.\n */\nfunction codeForHTTPStatus(status: number): FunctionsErrorCode {\n  // Make sure any successful status is OK.\n  if (status >= 200 && status < 300) {\n    return 'ok';\n  }\n  switch (status) {\n    case 0:\n      // This can happen if the server returns 500.\n      return 'internal';\n    case 400:\n      return 'invalid-argument';\n    case 401:\n      return 'unauthenticated';\n    case 403:\n      return 'permission-denied';\n    case 404:\n      return 'not-found';\n    case 409:\n      return 'aborted';\n    case 429:\n      return 'resource-exhausted';\n    case 499:\n      return 'cancelled';\n    case 500:\n      return 'internal';\n    case 501:\n      return 'unimplemented';\n    case 503:\n      return 'unavailable';\n    case 504:\n      return 'deadline-exceeded';\n    default: // ignore\n  }\n  return 'unknown';\n}\n\n/**\n * Takes an HTTP response and returns the corresponding Error, if any.\n */\nexport function _errorForResponse(\n  status: number,\n  bodyJSON: HttpResponseBody | null\n): Error | null {\n  let code = codeForHTTPStatus(status);\n\n  // Start with reasonable defaults from the status code.\n  let description: string = code;\n\n  let details: unknown = undefined;\n\n  // Then look through the body for explicit details.\n  try {\n    const errorJSON = bodyJSON && bodyJSON.error;\n    if (errorJSON) {\n      const status = errorJSON.status;\n      if (typeof status === 'string') {\n        if (!errorCodeMap[status]) {\n          // They must've included an unknown error code in the body.\n          return new FunctionsError('internal', 'internal');\n        }\n        code = errorCodeMap[status];\n\n        // TODO(klimt): Add better default descriptions for error enums.\n        // The default description needs to be updated for the new code.\n        description = status;\n      }\n\n      const message = errorJSON.message;\n      if (typeof message === 'string') {\n        description = message;\n      }\n\n      details = errorJSON.details;\n      if (details !== undefined) {\n        details = decode(details);\n      }\n    }\n  } catch (e) {\n    // If we couldn't parse explicit error data, that's fine.\n  }\n\n  if (code === 'ok') {\n    // Technically, there's an edge case where a developer could explicitly\n    // return an error code of OK, and we will treat it as success, but that\n    // seems reasonable.\n    return null;\n  }\n\n  return new FunctionsError(code, description, details);\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from '@firebase/component';\nimport { _isFirebaseServerApp, FirebaseApp } from '@firebase/app';\nimport {\n  AppCheckInternalComponentName,\n  FirebaseAppCheckInternal\n} from '@firebase/app-check-interop-types';\nimport {\n  MessagingInternal,\n  MessagingInternalComponentName\n} from '@firebase/messaging-interop-types';\nimport {\n  FirebaseAuthInternal,\n  FirebaseAuthInternalName\n} from '@firebase/auth-interop-types';\n\n/**\n * The metadata that should be supplied with function calls.\n * @internal\n */\nexport interface Context {\n  authToken?: string;\n  messagingToken?: string;\n  appCheckToken: string | null;\n}\n\n/**\n * Helper class to get metadata that should be included with a function call.\n * @internal\n */\nexport class ContextProvider {\n  private auth: FirebaseAuthInternal | null = null;\n  private messaging: MessagingInternal | null = null;\n  private appCheck: FirebaseAppCheckInternal | null = null;\n  private serverAppAppCheckToken: string | null = null;\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>\n  ) {\n    if (_isFirebaseServerApp(app) && app.settings.appCheckToken) {\n      this.serverAppAppCheckToken = app.settings.appCheckToken;\n    }\n    this.auth = authProvider.getImmediate({ optional: true });\n    this.messaging = messagingProvider.getImmediate({\n      optional: true\n    });\n\n    if (!this.auth) {\n      authProvider.get().then(\n        auth => (this.auth = auth),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.messaging) {\n      messagingProvider.get().then(\n        messaging => (this.messaging = messaging),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n\n    if (!this.appCheck) {\n      appCheckProvider?.get().then(\n        appCheck => (this.appCheck = appCheck),\n        () => {\n          /* get() never rejects */\n        }\n      );\n    }\n  }\n\n  async getAuthToken(): Promise<string | undefined> {\n    if (!this.auth) {\n      return undefined;\n    }\n\n    try {\n      const token = await this.auth.getToken();\n      return token?.accessToken;\n    } catch (e) {\n      // If there's any error when trying to get the auth token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getMessagingToken(): Promise<string | undefined> {\n    if (\n      !this.messaging ||\n      !('Notification' in self) ||\n      Notification.permission !== 'granted'\n    ) {\n      return undefined;\n    }\n\n    try {\n      return await this.messaging.getToken();\n    } catch (e) {\n      // We don't warn on this, because it usually means messaging isn't set up.\n      // console.warn('Failed to retrieve instance id token.', e);\n\n      // If there's any error when trying to get the token, leave it off.\n      return undefined;\n    }\n  }\n\n  async getAppCheckToken(\n    limitedUseAppCheckTokens?: boolean\n  ): Promise<string | null> {\n    if (this.serverAppAppCheckToken) {\n      return this.serverAppAppCheckToken;\n    }\n    if (this.appCheck) {\n      const result = limitedUseAppCheckTokens\n        ? await this.appCheck.getLimitedUseToken()\n        : await this.appCheck.getToken();\n      if (result.error) {\n        // Do not send the App Check header to the functions endpoint if\n        // there was an error from the App Check exchange endpoint. The App\n        // Check SDK will already have logged the error to console.\n        return null;\n      }\n      return result.token;\n    }\n    return null;\n  }\n\n  async getContext(limitedUseAppCheckTokens?: boolean): Promise<Context> {\n    const authToken = await this.getAuthToken();\n    const messagingToken = await this.getMessagingToken();\n    const appCheckToken = await this.getAppCheckToken(limitedUseAppCheckTokens);\n    return { authToken, messagingToken, appCheckToken };\n  }\n}\n", "/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { FirebaseApp, _FirebaseService } from '@firebase/app';\nimport {\n  HttpsCallable,\n  HttpsCallableResult,\n  HttpsCallableStreamResult,\n  HttpsCallableOptions,\n  HttpsCallableStreamOptions\n} from './public-types';\nimport { _errorForResponse, FunctionsError } from './error';\nimport { ContextProvider } from './context';\nimport { encode, decode } from './serializer';\nimport { Provider } from '@firebase/component';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport {\n  isCloudWorkstation,\n  pingServer,\n  updateEmulatorBanner\n} from '@firebase/util';\n\nexport const DEFAULT_REGION = 'us-central1';\n\nconst responseLineRE = /^data: (.*?)(?:\\n|$)/;\n\n/**\n * The response to an http request.\n */\ninterface HttpResponse {\n  status: number;\n  json: HttpResponseBody | null;\n}\n/**\n * Describes the shape of the HttpResponse body.\n * It makes functions that would otherwise take {} able to access the\n * possible elements in the body more easily\n */\nexport interface HttpResponseBody {\n  data?: unknown;\n  result?: unknown;\n  error?: {\n    message?: unknown;\n    status?: unknown;\n    details?: unknown;\n  };\n}\n\ninterface CancellablePromise<T> {\n  promise: Promise<T>;\n  cancel: () => void;\n}\n\n/**\n * Returns a Promise that will be rejected after the given duration.\n * The error will be of type FunctionsError.\n *\n * @param millis Number of milliseconds to wait before rejecting.\n */\nfunction failAfter(millis: number): CancellablePromise<never> {\n  // Node timers and browser timers are fundamentally incompatible, but we\n  // don't care about the value here\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  let timer: any | null = null;\n  return {\n    promise: new Promise((_, reject) => {\n      timer = setTimeout(() => {\n        reject(new FunctionsError('deadline-exceeded', 'deadline-exceeded'));\n      }, millis);\n    }),\n    cancel: () => {\n      if (timer) {\n        clearTimeout(timer);\n      }\n    }\n  };\n}\n\n/**\n * The main class for the Firebase Functions SDK.\n * @internal\n */\nexport class FunctionsService implements _FirebaseService {\n  readonly contextProvider: ContextProvider;\n  emulatorOrigin: string | null = null;\n  cancelAllRequests: Promise<void>;\n  deleteService!: () => Promise<void>;\n  region: string;\n  customDomain: string | null;\n\n  /**\n   * Creates a new Functions service for the given app.\n   * @param app - The FirebaseApp to use.\n   */\n  constructor(\n    readonly app: FirebaseApp,\n    authProvider: Provider<FirebaseAuthInternalName>,\n    messagingProvider: Provider<MessagingInternalComponentName>,\n    appCheckProvider: Provider<AppCheckInternalComponentName>,\n    regionOrCustomDomain: string = DEFAULT_REGION,\n    readonly fetchImpl: typeof fetch = (...args) => fetch(...args)\n  ) {\n    this.contextProvider = new ContextProvider(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider\n    );\n    // Cancels all ongoing requests when resolved.\n    this.cancelAllRequests = new Promise(resolve => {\n      this.deleteService = () => {\n        return Promise.resolve(resolve());\n      };\n    });\n\n    // Resolve the region or custom domain overload by attempting to parse it.\n    try {\n      const url = new URL(regionOrCustomDomain);\n      this.customDomain =\n        url.origin + (url.pathname === '/' ? '' : url.pathname);\n      this.region = DEFAULT_REGION;\n    } catch (e) {\n      this.customDomain = null;\n      this.region = regionOrCustomDomain;\n    }\n  }\n\n  _delete(): Promise<void> {\n    return this.deleteService();\n  }\n\n  /**\n   * Returns the URL for a callable with the given name.\n   * @param name - The name of the callable.\n   * @internal\n   */\n  _url(name: string): string {\n    const projectId = this.app.options.projectId;\n    if (this.emulatorOrigin !== null) {\n      const origin = this.emulatorOrigin;\n      return `${origin}/${projectId}/${this.region}/${name}`;\n    }\n\n    if (this.customDomain !== null) {\n      return `${this.customDomain}/${name}`;\n    }\n\n    return `https://${this.region}-${projectId}.cloudfunctions.net/${name}`;\n  }\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host The emulator host (ex: localhost)\n * @param port The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: FunctionsService,\n  host: string,\n  port: number\n): void {\n  const useSsl = isCloudWorkstation(host);\n  functionsInstance.emulatorOrigin = `http${\n    useSsl ? 's' : ''\n  }://${host}:${port}`;\n  // Workaround to get cookies in Firebase Studio\n  if (useSsl) {\n    void pingServer(functionsInstance.emulatorOrigin);\n    updateEmulatorBanner('Functions', true);\n  }\n}\n\n/**\n * Returns a reference to the callable https trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<RequestData, ResponseData, StreamData = unknown>(\n  functionsInstance: FunctionsService,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return call(functionsInstance, name, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return stream(functionsInstance, name, data, options);\n  };\n\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Returns a reference to the callable https trigger with the given url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData,\n  ResponseData,\n  StreamData = unknown\n>(\n  functionsInstance: FunctionsService,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  const callable = (\n    data?: RequestData | null\n  ): Promise<HttpsCallableResult> => {\n    return callAtURL(functionsInstance, url, data, options || {});\n  };\n\n  callable.stream = (\n    data?: RequestData | null,\n    options?: HttpsCallableStreamOptions\n  ) => {\n    return streamAtURL(functionsInstance, url, data, options || {});\n  };\n  return callable as HttpsCallable<RequestData, ResponseData, StreamData>;\n}\n\n/**\n * Does an HTTP POST and returns the completed response.\n * @param url The url to post to.\n * @param body The JSON body of the post.\n * @param headers The HTTP headers to include in the request.\n * @return A Promise that will succeed when the request finishes.\n */\nasync function postJSON(\n  url: string,\n  body: unknown,\n  headers: { [key: string]: string },\n  fetchImpl: typeof fetch\n): Promise<HttpResponse> {\n  headers['Content-Type'] = 'application/json';\n\n  let response: Response;\n  try {\n    response = await fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    });\n  } catch (e) {\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    return {\n      status: 0,\n      json: null\n    };\n  }\n  let json: HttpResponseBody | null = null;\n  try {\n    json = await response.json();\n  } catch (e) {\n    // If we fail to parse JSON, it will fail the same as an empty body.\n  }\n  return {\n    status: response.status,\n    json\n  };\n}\n\n/**\n * Creates authorization headers for Firebase Functions requests.\n * @param functionsInstance The Firebase Functions service instance.\n * @param options Options for the callable function, including AppCheck token settings.\n * @return A Promise that resolves a headers map to include in outgoing fetch request.\n */\nasync function makeAuthHeaders(\n  functionsInstance: FunctionsService,\n  options: HttpsCallableOptions\n): Promise<Record<string, string>> {\n  const headers: Record<string, string> = {};\n  const context = await functionsInstance.contextProvider.getContext(\n    options.limitedUseAppCheckTokens\n  );\n  if (context.authToken) {\n    headers['Authorization'] = 'Bearer ' + context.authToken;\n  }\n  if (context.messagingToken) {\n    headers['Firebase-Instance-ID-Token'] = context.messagingToken;\n  }\n  if (context.appCheckToken !== null) {\n    headers['X-Firebase-AppCheck'] = context.appCheckToken;\n  }\n  return headers;\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nfunction call(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  const url = functionsInstance._url(name);\n  return callAtURL(functionsInstance, url, data, options);\n}\n\n/**\n * Calls a callable function asynchronously and returns the result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n */\nasync function callAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableOptions\n): Promise<HttpsCallableResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n\n  // Default timeout to 70s, but let the options override it.\n  const timeout = options.timeout || 70000;\n\n  const failAfterHandle = failAfter(timeout);\n  const response = await Promise.race([\n    postJSON(url, body, headers, functionsInstance.fetchImpl),\n    failAfterHandle.promise,\n    functionsInstance.cancelAllRequests\n  ]);\n\n  // Always clear the failAfter timeout\n  failAfterHandle.cancel();\n\n  // If service was deleted, interrupted response throws an error.\n  if (!response) {\n    throw new FunctionsError(\n      'cancelled',\n      'Firebase Functions instance was deleted.'\n    );\n  }\n\n  // Check for an error status, regardless of http status.\n  const error = _errorForResponse(response.status, response.json);\n  if (error) {\n    throw error;\n  }\n\n  if (!response.json) {\n    throw new FunctionsError('internal', 'Response is not valid JSON object.');\n  }\n\n  let responseData = response.json.data;\n  // TODO(klimt): For right now, allow \"result\" instead of \"data\", for\n  // backwards compatibility.\n  if (typeof responseData === 'undefined') {\n    responseData = response.json.result;\n  }\n  if (typeof responseData === 'undefined') {\n    // Consider the response malformed.\n    throw new FunctionsError('internal', 'Response is missing data field.');\n  }\n\n  // Decode any special types, such as dates, in the returned data.\n  const decodedData = decode(responseData);\n\n  return { data: decodedData };\n}\n\n/**\n * Calls a callable function asynchronously and returns a streaming result.\n * @param name The name of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nfunction stream(\n  functionsInstance: FunctionsService,\n  name: string,\n  data: unknown,\n  options?: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  const url = functionsInstance._url(name);\n  return streamAtURL(functionsInstance, url, data, options || {});\n}\n\n/**\n * Calls a callable function asynchronously and return a streaming result.\n * @param url The url of the callable trigger.\n * @param data The data to pass as params to the function.\n * @param options Streaming request options.\n */\nasync function streamAtURL(\n  functionsInstance: FunctionsService,\n  url: string,\n  data: unknown,\n  options: HttpsCallableStreamOptions\n): Promise<HttpsCallableStreamResult> {\n  // Encode any special types, such as dates, in the input data.\n  data = encode(data);\n  const body = { data };\n  //\n  // Add a header for the authToken.\n  const headers = await makeAuthHeaders(functionsInstance, options);\n  headers['Content-Type'] = 'application/json';\n  headers['Accept'] = 'text/event-stream';\n\n  let response: Response;\n  try {\n    response = await functionsInstance.fetchImpl(url, {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers,\n      signal: options?.signal\n    });\n  } catch (e) {\n    if (e instanceof Error && e.name === 'AbortError') {\n      const error = new FunctionsError('cancelled', 'Request was cancelled.');\n      return {\n        data: Promise.reject(error),\n        stream: {\n          [Symbol.asyncIterator]() {\n            return {\n              next() {\n                return Promise.reject(error);\n              }\n            };\n          }\n        }\n      };\n    }\n    // This could be an unhandled error on the backend, or it could be a\n    // network error. There's no way to know, since an unhandled error on the\n    // backend will fail to set the proper CORS header, and thus will be\n    // treated as a network error by fetch.\n    const error = _errorForResponse(0, null);\n    return {\n      data: Promise.reject(error),\n      // Return an empty async iterator\n      stream: {\n        [Symbol.asyncIterator]() {\n          return {\n            next() {\n              return Promise.reject(error);\n            }\n          };\n        }\n      }\n    };\n  }\n  let resultResolver: (value: unknown) => void;\n  let resultRejecter: (reason: unknown) => void;\n  const resultPromise = new Promise<unknown>((resolve, reject) => {\n    resultResolver = resolve;\n    resultRejecter = reject;\n  });\n  options?.signal?.addEventListener('abort', () => {\n    const error = new FunctionsError('cancelled', 'Request was cancelled.');\n    resultRejecter(error);\n  });\n  const reader = response.body!.getReader();\n  const rstream = createResponseStream(\n    reader,\n    resultResolver!,\n    resultRejecter!,\n    options?.signal\n  );\n  return {\n    stream: {\n      [Symbol.asyncIterator]() {\n        const rreader = rstream.getReader();\n        return {\n          async next() {\n            const { value, done } = await rreader.read();\n            return { value: value as unknown, done };\n          },\n          async return() {\n            await rreader.cancel();\n            return { done: true, value: undefined };\n          }\n        };\n      }\n    },\n    data: resultPromise\n  };\n}\n\n/**\n * Creates a ReadableStream that processes a streaming response from a streaming\n * callable function that returns data in server-sent event format.\n *\n * @param reader The underlying reader providing raw response data\n * @param resultResolver Callback to resolve the final result when received\n * @param resultRejecter Callback to reject with an error if encountered\n * @param signal Optional AbortSignal to cancel the stream processing\n * @returns A ReadableStream that emits decoded messages from the response\n *\n * The returned ReadableStream:\n *   1. Emits individual messages when \"message\" data is received\n *   2. Resolves with the final result when a \"result\" message is received\n *   3. Rejects with an error if an \"error\" message is received\n */\nfunction createResponseStream(\n  reader: ReadableStreamDefaultReader<Uint8Array>,\n  resultResolver: (value: unknown) => void,\n  resultRejecter: (reason: unknown) => void,\n  signal?: AbortSignal\n): ReadableStream<unknown> {\n  const processLine = (\n    line: string,\n    controller: ReadableStreamDefaultController\n  ): void => {\n    const match = line.match(responseLineRE);\n    // ignore all other lines (newline, comments, etc.)\n    if (!match) {\n      return;\n    }\n    const data = match[1];\n    try {\n      const jsonData = JSON.parse(data);\n      if ('result' in jsonData) {\n        resultResolver(decode(jsonData.result));\n        return;\n      }\n      if ('message' in jsonData) {\n        controller.enqueue(decode(jsonData.message));\n        return;\n      }\n      if ('error' in jsonData) {\n        const error = _errorForResponse(0, jsonData);\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n    } catch (error) {\n      if (error instanceof FunctionsError) {\n        controller.error(error);\n        resultRejecter(error);\n        return;\n      }\n      // ignore other parsing errors\n    }\n  };\n\n  const decoder = new TextDecoder();\n  return new ReadableStream({\n    start(controller) {\n      let currentText = '';\n      return pump();\n      async function pump(): Promise<void> {\n        if (signal?.aborted) {\n          const error = new FunctionsError(\n            'cancelled',\n            'Request was cancelled'\n          );\n          controller.error(error);\n          resultRejecter(error);\n          return Promise.resolve();\n        }\n        try {\n          const { value, done } = await reader.read();\n          if (done) {\n            if (currentText.trim()) {\n              processLine(currentText.trim(), controller);\n            }\n            controller.close();\n            return;\n          }\n          if (signal?.aborted) {\n            const error = new FunctionsError(\n              'cancelled',\n              'Request was cancelled'\n            );\n            controller.error(error);\n            resultRejecter(error);\n            await reader.cancel();\n            return;\n          }\n          currentText += decoder.decode(value, { stream: true });\n          const lines = currentText.split('\\n');\n          currentText = lines.pop() || '';\n          for (const line of lines) {\n            if (line.trim()) {\n              processLine(line.trim(), controller);\n            }\n          }\n          return pump();\n        } catch (error) {\n          const functionsError =\n            error instanceof FunctionsError\n              ? error\n              : _errorForResponse(0, null);\n          controller.error(functionsError);\n          resultRejecter(functionsError);\n        }\n      }\n    },\n    cancel() {\n      return reader.cancel();\n    }\n  });\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _registerComponent, registerVersion } from '@firebase/app';\nimport { FunctionsService } from './service';\nimport {\n  Component,\n  ComponentType,\n  ComponentContainer,\n  InstanceFactory\n} from '@firebase/component';\nimport { FUNCTIONS_TYPE } from './constants';\nimport { FirebaseAuthInternalName } from '@firebase/auth-interop-types';\nimport { AppCheckInternalComponentName } from '@firebase/app-check-interop-types';\nimport { MessagingInternalComponentName } from '@firebase/messaging-interop-types';\nimport { name, version } from '../package.json';\n\nconst AUTH_INTERNAL_NAME: FirebaseAuthInternalName = 'auth-internal';\nconst APP_CHECK_INTERNAL_NAME: AppCheckInternalComponentName =\n  'app-check-internal';\nconst MESSAGING_INTERNAL_NAME: MessagingInternalComponentName =\n  'messaging-internal';\n\nexport function registerFunctions(variant?: string): void {\n  const factory: InstanceFactory<'functions'> = (\n    container: ComponentContainer,\n    { instanceIdentifier: regionOrCustomDomain }\n  ) => {\n    // Dependencies\n    const app = container.getProvider('app').getImmediate();\n    const authProvider = container.getProvider(AUTH_INTERNAL_NAME);\n    const messagingProvider = container.getProvider(MESSAGING_INTERNAL_NAME);\n    const appCheckProvider = container.getProvider(APP_CHECK_INTERNAL_NAME);\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    return new FunctionsService(\n      app,\n      authProvider,\n      messagingProvider,\n      appCheckProvider,\n      regionOrCustomDomain\n    );\n  };\n\n  _registerComponent(\n    new Component(\n      FUNCTIONS_TYPE,\n      factory,\n      ComponentType.PUBLIC\n    ).setMultipleInstances(true)\n  );\n\n  registerVersion(name, version, variant);\n  // BUILD_TARGET will be replaced by values like esm2017, cjs2017, etc during the compilation\n  registerVersion(name, version, '__BUILD_TARGET__');\n}\n", "/**\n * @license\n * Copyright 2020 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { _getProvider, FirebaseApp, getApp } from '@firebase/app';\nimport { FUNCTIONS_TYPE } from './constants';\n\nimport { Provider } from '@firebase/component';\nimport { Functions, HttpsCallableOptions, HttpsCallable } from './public-types';\nimport {\n  FunctionsService,\n  DEFAULT_REGION,\n  connectFunctionsEmulator as _connectFunctionsEmulator,\n  httpsCallable as _httpsCallable,\n  httpsCallableFromURL as _httpsCallableFromURL\n} from './service';\nimport {\n  getModularInstance,\n  getDefaultEmulatorHostnameAndPort\n} from '@firebase/util';\n\nexport { FunctionsError } from './error';\nexport * from './public-types';\n\n/**\n * Returns a {@link Functions} instance for the given app.\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\n * @param regionOrCustomDomain - one of:\n *   a) The region the callable functions are located in (ex: us-central1)\n *   b) A custom domain hosting the callable functions (ex: https://mydomain.com)\n * @public\n */\nexport function getFunctions(\n  app: FirebaseApp = getApp(),\n  regionOrCustomDomain: string = DEFAULT_REGION\n): Functions {\n  // Dependencies\n  const functionsProvider: Provider<'functions'> = _getProvider(\n    getModularInstance(app),\n    FUNCTIONS_TYPE\n  );\n  const functionsInstance = functionsProvider.getImmediate({\n    identifier: regionOrCustomDomain\n  });\n  const emulator = getDefaultEmulatorHostnameAndPort('functions');\n  if (emulator) {\n    connectFunctionsEmulator(functionsInstance, ...emulator);\n  }\n  return functionsInstance;\n}\n\n/**\n * Modify this instance to communicate with the Cloud Functions emulator.\n *\n * Note: this must be called before this instance has been used to do any operations.\n *\n * @param host - The emulator host (ex: localhost)\n * @param port - The emulator port (ex: 5001)\n * @public\n */\nexport function connectFunctionsEmulator(\n  functionsInstance: Functions,\n  host: string,\n  port: number\n): void {\n  _connectFunctionsEmulator(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    host,\n    port\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the given name.\n * @param name - The name of the trigger.\n * @public\n */\nexport function httpsCallable<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  name: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallable<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    name,\n    options\n  );\n}\n\n/**\n * Returns a reference to the callable HTTPS trigger with the specified url.\n * @param url - The url of the trigger.\n * @public\n */\nexport function httpsCallableFromURL<\n  RequestData = unknown,\n  ResponseData = unknown,\n  StreamData = unknown\n>(\n  functionsInstance: Functions,\n  url: string,\n  options?: HttpsCallableOptions\n): HttpsCallable<RequestData, ResponseData, StreamData> {\n  return _httpsCallableFromURL<RequestData, ResponseData, StreamData>(\n    getModularInstance<FunctionsService>(functionsInstance as FunctionsService),\n    url,\n    options\n  );\n}\n", "/**\n * Cloud Functions for Firebase\n *\n * @packageDocumentation\n */\n\n/**\n * @license\n * Copyright 2017 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport { registerFunctions } from './config';\n\nexport * from './api';\nexport * from './public-types';\n\nregisterFunctions();\n"], "mappings": ";;;;;;;;;;;;;;;;;AAgBA,IAAM,YAAY;AAClB,IAAM,qBAAqB;AAE3B,SAAS,UAGP,GACA,GAA6B;AAE7B,QAAM,SAAqC,CAAA;AAC3C,aAAW,OAAO,GAAG;AACnB,QAAI,EAAE,eAAe,GAAG,GAAG;AACzB,aAAO,GAAG,IAAI,EAAE,EAAE,GAAG,CAAC;;;AAG1B,SAAO;AACT;AAQM,SAAU,OAAO,MAAa;AAClC,MAAI,QAAQ,MAAM;AAChB,WAAO;;AAET,MAAI,gBAAgB,QAAQ;AAC1B,WAAO,KAAK,QAAO;;AAErB,MAAI,OAAO,SAAS,YAAY,SAAS,IAAI,GAAG;AAG9C,WAAO;;AAET,MAAI,SAAS,QAAQ,SAAS,OAAO;AACnC,WAAO;;AAET,MAAI,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM,mBAAmB;AAC9D,WAAO;;AAET,MAAI,gBAAgB,MAAM;AACxB,WAAO,KAAK,YAAW;;AAEzB,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO,KAAK,IAAI,OAAK,OAAO,CAAC,CAAC;;AAEhC,MAAI,OAAO,SAAS,cAAc,OAAO,SAAS,UAAU;AAC1D,WAAO,UAAU,MAAO,OAAK,OAAO,CAAC,CAAC;;AAGxC,QAAM,IAAI,MAAM,qCAAqC,IAAI;AAC3D;AAQM,SAAU,OAAO,MAAa;AAClC,MAAI,QAAQ,MAAM;AAChB,WAAO;;AAET,MAAK,KAAoC,OAAO,GAAG;AACjD,YAAS,KAAoC,OAAO,GAAC;MACnD,KAAK;;MAEL,KAAK,oBAAoB;AAIvB,cAAM,QAAQ,OAAQ,KAAoC,OAAO,CAAC;AAClE,YAAI,MAAM,KAAK,GAAG;AAChB,gBAAM,IAAI,MAAM,uCAAuC,IAAI;;AAE7D,eAAO;;MAET,SAAS;AACP,cAAM,IAAI,MAAM,uCAAuC,IAAI;;;;AAIjE,MAAI,MAAM,QAAQ,IAAI,GAAG;AACvB,WAAO,KAAK,IAAI,OAAK,OAAO,CAAC,CAAC;;AAEhC,MAAI,OAAO,SAAS,cAAc,OAAO,SAAS,UAAU;AAC1D,WAAO,UAAU,MAAO,OAAK,OAAO,CAAC,CAAC;;AAGxC,SAAO;AACT;ACxFO,IAAM,iBAAiB;ACU9B,IAAM,eAAuD;EAC3D,IAAI;EACJ,WAAW;EACX,SAAS;EACT,kBAAkB;EAClB,mBAAmB;EACnB,WAAW;EACX,gBAAgB;EAChB,mBAAmB;EACnB,iBAAiB;EACjB,oBAAoB;EACpB,qBAAqB;EACrB,SAAS;EACT,cAAc;EACd,eAAe;EACf,UAAU;EACV,aAAa;EACb,WAAW;;AAUP,IAAO,iBAAP,MAAO,wBAAuB,cAAa;;;;EAI/C,YAKE,MACA,SAIS,SAAiB;AAE1B,UAAM,GAAG,cAAc,IAAI,IAAI,IAAI,WAAW,EAAE;AAFvC,SAAO,UAAP;AAMT,WAAO,eAAe,MAAM,gBAAe,SAAS;;AAEvD;AAUD,SAAS,kBAAkB,QAAc;AAEvC,MAAI,UAAU,OAAO,SAAS,KAAK;AACjC,WAAO;;AAET,UAAQ,QAAM;IACZ,KAAK;AAEH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;;AAGX,SAAO;AACT;AAKgB,SAAA,kBACd,QACA,UAAiC;AAEjC,MAAI,OAAO,kBAAkB,MAAM;AAGnC,MAAI,cAAsB;AAE1B,MAAI,UAAmB;AAGvB,MAAI;AACF,UAAM,YAAY,YAAY,SAAS;AACvC,QAAI,WAAW;AACb,YAAMA,UAAS,UAAU;AACzB,UAAI,OAAOA,YAAW,UAAU;AAC9B,YAAI,CAAC,aAAaA,OAAM,GAAG;AAEzB,iBAAO,IAAI,eAAe,YAAY,UAAU;;AAElD,eAAO,aAAaA,OAAM;AAI1B,sBAAcA;;AAGhB,YAAM,UAAU,UAAU;AAC1B,UAAI,OAAO,YAAY,UAAU;AAC/B,sBAAc;;AAGhB,gBAAU,UAAU;AACpB,UAAI,YAAY,QAAW;AACzB,kBAAU,OAAO,OAAO;;;WAGrB,GAAG;;AAIZ,MAAI,SAAS,MAAM;AAIjB,WAAO;;AAGT,SAAO,IAAI,eAAe,MAAM,aAAa,OAAO;AACtD;ICpIa,wBAAe;EAK1B,YACW,KACT,cACA,mBACA,kBAAyD;AAHhD,SAAG,MAAH;AALH,SAAI,OAAgC;AACpC,SAAS,YAA6B;AACtC,SAAQ,WAAoC;AAC5C,SAAsB,yBAAkB;AAO9C,QAAI,qBAAqB,GAAG,KAAK,IAAI,SAAS,eAAe;AAC3D,WAAK,yBAAyB,IAAI,SAAS;;AAE7C,SAAK,OAAO,aAAa,aAAa,EAAE,UAAU,KAAI,CAAE;AACxD,SAAK,YAAY,kBAAkB,aAAa;MAC9C,UAAU;IACX,CAAA;AAED,QAAI,CAAC,KAAK,MAAM;AACd,mBAAa,IAAG,EAAG,KACjB,UAAS,KAAK,OAAO,MACrB,MAAK;MAEL,CAAC;;AAIL,QAAI,CAAC,KAAK,WAAW;AACnB,wBAAkB,IAAG,EAAG,KACtB,eAAc,KAAK,YAAY,WAC/B,MAAK;MAEL,CAAC;;AAIL,QAAI,CAAC,KAAK,UAAU;AAClB,2BAAgB,QAAhB,qBAAA,SAAA,SAAA,iBAAkB,IAAG,EAAG,KACtB,cAAa,KAAK,WAAW,UAC7B,MAAK;MAEL,CAAC;;;EAKP,MAAM,eAAY;AAChB,QAAI,CAAC,KAAK,MAAM;AACd,aAAO;;AAGT,QAAI;AACF,YAAM,QAAQ,MAAM,KAAK,KAAK,SAAQ;AACtC,aAAO,UAAA,QAAA,UAAK,SAAA,SAAL,MAAO;aACP,GAAG;AAEV,aAAO;;;EAIX,MAAM,oBAAiB;AACrB,QACE,CAAC,KAAK,aACN,EAAE,kBAAkB,SACpB,aAAa,eAAe,WAC5B;AACA,aAAO;;AAGT,QAAI;AACF,aAAO,MAAM,KAAK,UAAU,SAAQ;aAC7B,GAAG;AAKV,aAAO;;;EAIX,MAAM,iBACJ,0BAAkC;AAElC,QAAI,KAAK,wBAAwB;AAC/B,aAAO,KAAK;;AAEd,QAAI,KAAK,UAAU;AACjB,YAAM,SAAS,2BACX,MAAM,KAAK,SAAS,mBAAkB,IACtC,MAAM,KAAK,SAAS,SAAQ;AAChC,UAAI,OAAO,OAAO;AAIhB,eAAO;;AAET,aAAO,OAAO;;AAEhB,WAAO;;EAGT,MAAM,WAAW,0BAAkC;AACjD,UAAM,YAAY,MAAM,KAAK,aAAY;AACzC,UAAM,iBAAiB,MAAM,KAAK,kBAAiB;AACnD,UAAM,gBAAgB,MAAM,KAAK,iBAAiB,wBAAwB;AAC1E,WAAO,EAAE,WAAW,gBAAgB,cAAa;;AAEpD;ACpHM,IAAM,iBAAiB;AAE9B,IAAM,iBAAiB;AAmCvB,SAAS,UAAU,QAAc;AAI/B,MAAI,QAAoB;AACxB,SAAO;IACL,SAAS,IAAI,QAAQ,CAAC,GAAG,WAAU;AACjC,cAAQ,WAAW,MAAK;AACtB,eAAO,IAAI,eAAe,qBAAqB,mBAAmB,CAAC;SAClE,MAAM;IACX,CAAC;IACD,QAAQ,MAAK;AACX,UAAI,OAAO;AACT,qBAAa,KAAK;;;;AAI1B;IAMa,yBAAgB;;;;;EAY3B,YACW,KACT,cACA,mBACA,kBACA,uBAA+B,gBACtB,YAA0B,IAAI,SAAS,MAAM,GAAG,IAAI,GAAC;AALrD,SAAG,MAAH;AAKA,SAAS,YAAT;AAhBX,SAAc,iBAAkB;AAkB9B,SAAK,kBAAkB,IAAI,gBACzB,KACA,cACA,mBACA,gBAAgB;AAGlB,SAAK,oBAAoB,IAAI,QAAQ,aAAU;AAC7C,WAAK,gBAAgB,MAAK;AACxB,eAAO,QAAQ,QAAQ,QAAO,CAAE;MAClC;IACF,CAAC;AAGD,QAAI;AACF,YAAM,MAAM,IAAI,IAAI,oBAAoB;AACxC,WAAK,eACH,IAAI,UAAU,IAAI,aAAa,MAAM,KAAK,IAAI;AAChD,WAAK,SAAS;aACP,GAAG;AACV,WAAK,eAAe;AACpB,WAAK,SAAS;;;EAIlB,UAAO;AACL,WAAO,KAAK,cAAa;;;;;;;EAQ3B,KAAKC,OAAY;AACf,UAAM,YAAY,KAAK,IAAI,QAAQ;AACnC,QAAI,KAAK,mBAAmB,MAAM;AAChC,YAAM,SAAS,KAAK;AACpB,aAAO,GAAG,MAAM,IAAI,SAAS,IAAI,KAAK,MAAM,IAAIA,KAAI;;AAGtD,QAAI,KAAK,iBAAiB,MAAM;AAC9B,aAAO,GAAG,KAAK,YAAY,IAAIA,KAAI;;AAGrC,WAAO,WAAW,KAAK,MAAM,IAAI,SAAS,uBAAuBA,KAAI;;AAExE;SAWeC,2BACd,mBACA,MACA,MAAY;AAEZ,QAAM,SAAS,mBAAmB,IAAI;AACtC,oBAAkB,iBAAiB,OACjC,SAAS,MAAM,EACjB,MAAM,IAAI,IAAI,IAAI;AAElB,MAAI,QAAQ;AACV,SAAK,WAAW,kBAAkB,cAAc;AAChD,yBAAqB,aAAa,IAAI;;AAE1C;SAOgBC,gBACd,mBACAF,OACA,SAA8B;AAE9B,QAAM,WAAW,CACf,SACgC;AAChC,WAAO,KAAK,mBAAmBA,OAAM,MAAM,WAAW,CAAA,CAAE;EAC1D;AAEA,WAAS,SAAS,CAChB,MACAG,aACE;AACF,WAAO,OAAO,mBAAmBH,OAAM,MAAMG,QAAO;EACtD;AAEA,SAAO;AACT;SAOgBC,uBAKd,mBACA,KACA,SAA8B;AAE9B,QAAM,WAAW,CACf,SACgC;AAChC,WAAO,UAAU,mBAAmB,KAAK,MAAM,WAAW,CAAA,CAAE;EAC9D;AAEA,WAAS,SAAS,CAChB,MACAD,aACE;AACF,WAAO,YAAY,mBAAmB,KAAK,MAAMA,YAAW,CAAA,CAAE;EAChE;AACA,SAAO;AACT;AASA,eAAe,SACb,KACA,MACA,SACA,WAAuB;AAEvB,UAAQ,cAAc,IAAI;AAE1B,MAAI;AACJ,MAAI;AACF,eAAW,MAAM,UAAU,KAAK;MAC9B,QAAQ;MACR,MAAM,KAAK,UAAU,IAAI;MACzB;IACD,CAAA;WACM,GAAG;AAKV,WAAO;MACL,QAAQ;MACR,MAAM;;;AAGV,MAAI,OAAgC;AACpC,MAAI;AACF,WAAO,MAAM,SAAS,KAAI;WACnB,GAAG;;AAGZ,SAAO;IACL,QAAQ,SAAS;IACjB;;AAEJ;AAQA,eAAe,gBACb,mBACA,SAA6B;AAE7B,QAAM,UAAkC,CAAA;AACxC,QAAM,UAAU,MAAM,kBAAkB,gBAAgB,WACtD,QAAQ,wBAAwB;AAElC,MAAI,QAAQ,WAAW;AACrB,YAAQ,eAAe,IAAI,YAAY,QAAQ;;AAEjD,MAAI,QAAQ,gBAAgB;AAC1B,YAAQ,4BAA4B,IAAI,QAAQ;;AAElD,MAAI,QAAQ,kBAAkB,MAAM;AAClC,YAAQ,qBAAqB,IAAI,QAAQ;;AAE3C,SAAO;AACT;AAOA,SAAS,KACP,mBACAH,OACA,MACA,SAA6B;AAE7B,QAAM,MAAM,kBAAkB,KAAKA,KAAI;AACvC,SAAO,UAAU,mBAAmB,KAAK,MAAM,OAAO;AACxD;AAOA,eAAe,UACb,mBACA,KACA,MACA,SAA6B;AAG7B,SAAO,OAAO,IAAI;AAClB,QAAM,OAAO,EAAE,KAAI;AAGnB,QAAM,UAAU,MAAM,gBAAgB,mBAAmB,OAAO;AAGhE,QAAM,UAAU,QAAQ,WAAW;AAEnC,QAAM,kBAAkB,UAAU,OAAO;AACzC,QAAM,WAAW,MAAM,QAAQ,KAAK;IAClC,SAAS,KAAK,MAAM,SAAS,kBAAkB,SAAS;IACxD,gBAAgB;IAChB,kBAAkB;EACnB,CAAA;AAGD,kBAAgB,OAAM;AAGtB,MAAI,CAAC,UAAU;AACb,UAAM,IAAI,eACR,aACA,0CAA0C;;AAK9C,QAAM,QAAQ,kBAAkB,SAAS,QAAQ,SAAS,IAAI;AAC9D,MAAI,OAAO;AACT,UAAM;;AAGR,MAAI,CAAC,SAAS,MAAM;AAClB,UAAM,IAAI,eAAe,YAAY,oCAAoC;;AAG3E,MAAI,eAAe,SAAS,KAAK;AAGjC,MAAI,OAAO,iBAAiB,aAAa;AACvC,mBAAe,SAAS,KAAK;;AAE/B,MAAI,OAAO,iBAAiB,aAAa;AAEvC,UAAM,IAAI,eAAe,YAAY,iCAAiC;;AAIxE,QAAM,cAAc,OAAO,YAAY;AAEvC,SAAO,EAAE,MAAM,YAAW;AAC5B;AAQA,SAAS,OACP,mBACAA,OACA,MACA,SAAoC;AAEpC,QAAM,MAAM,kBAAkB,KAAKA,KAAI;AACvC,SAAO,YAAY,mBAAmB,KAAK,MAAM,WAAW,CAAA,CAAE;AAChE;AAQA,eAAe,YACb,mBACA,KACA,MACA,SAAmC;;AAGnC,SAAO,OAAO,IAAI;AAClB,QAAM,OAAO,EAAE,KAAI;AAGnB,QAAM,UAAU,MAAM,gBAAgB,mBAAmB,OAAO;AAChE,UAAQ,cAAc,IAAI;AAC1B,UAAQ,QAAQ,IAAI;AAEpB,MAAI;AACJ,MAAI;AACF,eAAW,MAAM,kBAAkB,UAAU,KAAK;MAChD,QAAQ;MACR,MAAM,KAAK,UAAU,IAAI;MACzB;MACA,QAAQ,YAAO,QAAP,YAAA,SAAA,SAAA,QAAS;IAClB,CAAA;WACM,GAAG;AACV,QAAI,aAAa,SAAS,EAAE,SAAS,cAAc;AACjD,YAAMK,SAAQ,IAAI,eAAe,aAAa,wBAAwB;AACtE,aAAO;QACL,MAAM,QAAQ,OAAOA,MAAK;QAC1B,QAAQ;UACN,CAAC,OAAO,aAAa,IAAC;AACpB,mBAAO;cACL,OAAI;AACF,uBAAO,QAAQ,OAAOA,MAAK;;;;QAIlC;;;AAOL,UAAM,QAAQ,kBAAkB,GAAG,IAAI;AACvC,WAAO;MACL,MAAM,QAAQ,OAAO,KAAK;;MAE1B,QAAQ;QACN,CAAC,OAAO,aAAa,IAAC;AACpB,iBAAO;YACL,OAAI;AACF,qBAAO,QAAQ,OAAO,KAAK;;;;MAIlC;;;AAGL,MAAI;AACJ,MAAI;AACJ,QAAM,gBAAgB,IAAI,QAAiB,CAAC,SAAS,WAAU;AAC7D,qBAAiB;AACjB,qBAAiB;EACnB,CAAC;AACD,GAAA,KAAA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,YAAM,QAAA,OAAA,SAAA,SAAA,GAAE,iBAAiB,SAAS,MAAK;AAC9C,UAAM,QAAQ,IAAI,eAAe,aAAa,wBAAwB;AACtE,mBAAe,KAAK;EACtB,CAAC;AACD,QAAM,SAAS,SAAS,KAAM,UAAS;AACvC,QAAM,UAAU,qBACd,QACA,gBACA,gBACA,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,MAAM;AAEjB,SAAO;IACL,QAAQ;MACN,CAAC,OAAO,aAAa,IAAC;AACpB,cAAM,UAAU,QAAQ,UAAS;AACjC,eAAO;UACL,MAAM,OAAI;AACR,kBAAM,EAAE,OAAO,KAAI,IAAK,MAAM,QAAQ,KAAI;AAC1C,mBAAO,EAAE,OAAyB,KAAI;;UAExC,MAAM,SAAM;AACV,kBAAM,QAAQ,OAAM;AACpB,mBAAO,EAAE,MAAM,MAAM,OAAO,OAAS;;;;IAI5C;IACD,MAAM;;AAEV;AAiBA,SAAS,qBACP,QACA,gBACA,gBACA,QAAoB;AAEpB,QAAM,cAAc,CAClB,MACA,eACQ;AACR,UAAM,QAAQ,KAAK,MAAM,cAAc;AAEvC,QAAI,CAAC,OAAO;AACV;;AAEF,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI;AACF,YAAM,WAAW,KAAK,MAAM,IAAI;AAChC,UAAI,YAAY,UAAU;AACxB,uBAAe,OAAO,SAAS,MAAM,CAAC;AACtC;;AAEF,UAAI,aAAa,UAAU;AACzB,mBAAW,QAAQ,OAAO,SAAS,OAAO,CAAC;AAC3C;;AAEF,UAAI,WAAW,UAAU;AACvB,cAAM,QAAQ,kBAAkB,GAAG,QAAQ;AAC3C,mBAAW,MAAM,KAAK;AACtB,uBAAe,KAAK;AACpB;;aAEK,OAAO;AACd,UAAI,iBAAiB,gBAAgB;AACnC,mBAAW,MAAM,KAAK;AACtB,uBAAe,KAAK;AACpB;;;EAIN;AAEA,QAAM,UAAU,IAAI,YAAW;AAC/B,SAAO,IAAI,eAAe;IACxB,MAAM,YAAU;AACd,UAAI,cAAc;AAClB,aAAO,KAAI;AACX,qBAAe,OAAI;AACjB,YAAI,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,SAAS;AACnB,gBAAM,QAAQ,IAAI,eAChB,aACA,uBAAuB;AAEzB,qBAAW,MAAM,KAAK;AACtB,yBAAe,KAAK;AACpB,iBAAO,QAAQ,QAAO;;AAExB,YAAI;AACF,gBAAM,EAAE,OAAO,KAAI,IAAK,MAAM,OAAO,KAAI;AACzC,cAAI,MAAM;AACR,gBAAI,YAAY,KAAI,GAAI;AACtB,0BAAY,YAAY,KAAI,GAAI,UAAU;;AAE5C,uBAAW,MAAK;AAChB;;AAEF,cAAI,WAAM,QAAN,WAAM,SAAA,SAAN,OAAQ,SAAS;AACnB,kBAAM,QAAQ,IAAI,eAChB,aACA,uBAAuB;AAEzB,uBAAW,MAAM,KAAK;AACtB,2BAAe,KAAK;AACpB,kBAAM,OAAO,OAAM;AACnB;;AAEF,yBAAe,QAAQ,OAAO,OAAO,EAAE,QAAQ,KAAI,CAAE;AACrD,gBAAM,QAAQ,YAAY,MAAM,IAAI;AACpC,wBAAc,MAAM,IAAG,KAAM;AAC7B,qBAAW,QAAQ,OAAO;AACxB,gBAAI,KAAK,KAAI,GAAI;AACf,0BAAY,KAAK,KAAI,GAAI,UAAU;;;AAGvC,iBAAO,KAAI;iBACJ,OAAO;AACd,gBAAM,iBACJ,iBAAiB,iBACb,QACA,kBAAkB,GAAG,IAAI;AAC/B,qBAAW,MAAM,cAAc;AAC/B,yBAAe,cAAc;;;;IAInC,SAAM;AACJ,aAAO,OAAO,OAAM;;EAEvB,CAAA;AACH;;;ACtlBA,IAAM,qBAA+C;AACrD,IAAM,0BACJ;AACF,IAAM,0BACJ;AAEI,SAAU,kBAAkB,SAAgB;AAChD,QAAM,UAAwC,CAC5C,WACA,EAAE,oBAAoB,qBAAoB,MACxC;AAEF,UAAM,MAAM,UAAU,YAAY,KAAK,EAAE,aAAY;AACrD,UAAM,eAAe,UAAU,YAAY,kBAAkB;AAC7D,UAAM,oBAAoB,UAAU,YAAY,uBAAuB;AACvE,UAAM,mBAAmB,UAAU,YAAY,uBAAuB;AAGtE,WAAO,IAAI,iBACT,KACA,cACA,mBACA,kBACA,oBAAoB;EAExB;AAEA,qBACE,IAAI;IACF;IACA;IAED;;EAAA,EAAC,qBAAqB,IAAI,CAAC;AAG9B,kBAAgB,MAAM,SAAS,OAAO;AAEtC,kBAAgB,MAAM,SAAS,SAAkB;AACnD;ACxBM,SAAU,aACd,MAAmB,OAAM,GACzB,uBAA+B,gBAAc;AAG7C,QAAM,oBAA2C,aAC/C,mBAAmB,GAAG,GACtB,cAAc;AAEhB,QAAM,oBAAoB,kBAAkB,aAAa;IACvD,YAAY;EACb,CAAA;AACD,QAAM,WAAW,kCAAkC,WAAW;AAC9D,MAAI,UAAU;AACZ,6BAAyB,mBAAmB,GAAG,QAAQ;;AAEzD,SAAO;AACT;SAWgB,yBACd,mBACA,MACA,MAAY;AAEZC,6BACE,mBAAqC,iBAAqC,GAC1E,MACA,IAAI;AAER;SAOgB,cAKd,mBACAN,OACA,SAA8B;AAE9B,SAAOO,gBACL,mBAAqC,iBAAqC,GAC1EP,OACA,OAAO;AAEX;SAOgB,qBAKd,mBACA,KACA,SAA8B;AAE9B,SAAOQ,uBACL,mBAAqC,iBAAqC,GAC1E,KACA,OAAO;AAEX;AClGA,kBAAiB;", "names": ["status", "name", "connectFunctionsEmulator", "httpsCallable", "options", "httpsCallableFromURL", "error", "_connectFunctionsEmulator", "_httpsCallable", "_httpsCallableFromURL"]}