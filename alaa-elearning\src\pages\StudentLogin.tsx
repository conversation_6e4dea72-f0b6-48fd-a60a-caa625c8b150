import React, { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';

const StudentLogin: React.FC = () => {
  const [accessCode, setAccessCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Validate access code format
      if (!/^\d{7}$/.test(accessCode)) {
        throw new Error('كود الوصول يجب أن يكون 7 أرقام');
      }

      await login(accessCode);
      navigate('/dashboard');
    } catch (error: any) {
      setError(error.message || 'حدث خطأ أثناء تسجيل الدخول');
    } finally {
      setLoading(false);
    }
  };

  const handleAccessCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, ''); // Only allow digits
    if (value.length <= 7) {
      setAccessCode(value);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-white py-12 px-4">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-primary-blue rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="fas fa-user-graduate text-white text-2xl"></i>
          </div>
          <h1 className="text-3xl font-bold text-text-dark mb-2">
            تسجيل دخول الطلاب
          </h1>
          <p className="text-text-light">
            أدخل كود الوصول الخاص بك للدخول إلى المنصة
          </p>
        </div>

        {/* Login Form */}
        <div className="card">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Access Code Input */}
            <div className="form-group">
              <label htmlFor="accessCode" className="form-label">
                <i className="fas fa-key text-gold ml-2"></i>
                كود الوصول (7 أرقام)
              </label>
              <input
                type="text"
                id="accessCode"
                value={accessCode}
                onChange={handleAccessCodeChange}
                className="form-input text-center text-2xl font-mono tracking-widest"
                placeholder="0000000"
                maxLength={7}
                required
                disabled={loading}
                autoComplete="off"
              />
              <p className="text-xs text-text-light mt-2">
                كود الوصول مكون من 7 أرقام فقط
              </p>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <i className="fas fa-exclamation-triangle text-red-500 ml-2"></i>
                  <span className="text-red-700 text-sm">{error}</span>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={loading || accessCode.length !== 7}
              className="w-full btn btn-primary btn-lg"
            >
              {loading ? (
                <LoadingSpinner size="sm" text="" />
              ) : (
                <>
                  <i className="fas fa-sign-in-alt"></i>
                  دخول المنصة
                </>
              )}
            </button>
          </form>

          {/* Help Section */}
          <div className="mt-6 pt-6 border-t border-border-color">
            <div className="text-center">
              <h3 className="text-sm font-medium text-text-dark mb-3">
                تحتاج مساعدة؟
              </h3>
              <div className="space-y-2 text-sm text-text-light">
                <p className="flex items-center justify-center">
                  <i className="fas fa-info-circle text-primary-blue ml-2"></i>
                  كود الوصول يتم الحصول عليه من المدرب
                </p>
                <p className="flex items-center justify-center">
                  <i className="fas fa-phone text-primary-blue ml-2"></i>
                  للدعم: +20 ************
                </p>
                <p className="flex items-center justify-center">
                  <i className="fas fa-envelope text-primary-blue ml-2"></i>
                  <EMAIL>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center mt-6">
          <Link 
            to="/" 
            className="text-primary-blue hover:text-secondary-blue transition-colors text-sm"
          >
            <i className="fas fa-arrow-right ml-2"></i>
            العودة للرئيسية
          </Link>
        </div>

        {/* Features Preview */}
        <div className="mt-8 grid grid-cols-3 gap-4 text-center">
          <div className="p-3">
            <i className="fas fa-video text-primary-blue text-lg mb-2 block"></i>
            <span className="text-xs text-text-light">دروس فيديو</span>
          </div>
          <div className="p-3">
            <i className="fas fa-file-pdf text-primary-blue text-lg mb-2 block"></i>
            <span className="text-xs text-text-light">ملفات PDF</span>
          </div>
          <div className="p-3">
            <i className="fas fa-certificate text-primary-blue text-lg mb-2 block"></i>
            <span className="text-xs text-text-light">شهادات</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentLogin;
