import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { User } from './types';

// Components
import Header from './components/Header';
import Footer from './components/Footer';
import LoadingSpinner from './components/LoadingSpinner';

// Pages
import HomePage from './pages/HomePage';
import StudentLogin from './pages/StudentLogin';
import StudentDashboard from './pages/StudentDashboard';
import CourseView from './pages/CourseView';
import AdminLogin from './pages/AdminLogin';
import AdminDashboard from './pages/AdminDashboard';

// Services
import { getCurrentUser, checkAdminAuth } from './services/auth';

// Context
import { AuthProvider } from './contexts/AuthContext';

const App: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [isAdmin, setIsAdmin] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Check for existing user session
        const currentUser = await getCurrentUser();
        if (currentUser) {
          setUser(currentUser);
        }

        // Check for admin session
        const adminAuth = await checkAdminAuth();
        if (adminAuth) {
          setIsAdmin(true);
        }
      } catch (error) {
        console.error('Error initializing app:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeApp();
  }, []);

  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <AuthProvider>
      <Router>
        <div className="min-h-screen flex flex-col bg-gradient-to-br from-blue-50 to-white">
          <Header user={user} isAdmin={isAdmin} />
          
          <main className="flex-1">
            <Routes>
              {/* Public Routes */}
              <Route path="/" element={<HomePage />} />
              <Route path="/login" element={<StudentLogin />} />
              
              {/* Student Routes */}
              <Route 
                path="/dashboard" 
                element={
                  user ? <StudentDashboard /> : <Navigate to="/login" replace />
                } 
              />
              <Route 
                path="/course/:courseId" 
                element={
                  user ? <CourseView /> : <Navigate to="/login" replace />
                } 
              />
              
              {/* Admin Routes */}
              <Route 
                path={`/${import.meta.env.VITE_ADMIN_SECRET_PATH || 'admin'}`} 
                element={<AdminLogin />} 
              />
              <Route 
                path={`/${import.meta.env.VITE_ADMIN_SECRET_PATH || 'admin'}/dashboard`} 
                element={
                  isAdmin ? <AdminDashboard /> : <Navigate to={`/${import.meta.env.VITE_ADMIN_SECRET_PATH || 'admin'}`} replace />
                } 
              />
              
              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </main>
          
          <Footer />
        </div>
      </Router>
    </AuthProvider>
  );
};

export default App;
