import React, { useState, useEffect } from 'react';

interface SecurityEvent {
  id: string;
  event: string;
  userId?: string;
  userType: 'student' | 'admin';
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  success: boolean;
  details?: any;
}

interface SecurityAuditProps {
  isOpen: boolean;
  onClose: () => void;
}

const SecurityAudit: React.FC<SecurityAuditProps> = ({ isOpen, onClose }) => {
  const [events, setEvents] = useState<SecurityEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'success' | 'failed'>('all');
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d'>('24h');

  useEffect(() => {
    if (isOpen) {
      loadSecurityEvents();
    }
  }, [isOpen, filter, timeRange]);

  const loadSecurityEvents = async () => {
    setLoading(true);
    try {
      // Mock security events - in real app, fetch from backend
      const mockEvents: SecurityEvent[] = [
        {
          id: '1',
          event: 'Admin Login',
          userId: 'admin-1',
          userType: 'admin',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
          success: true
        },
        {
          id: '2',
          event: 'Student Login',
          userId: 'user-123',
          userType: 'student',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
          success: true
        },
        {
          id: '3',
          event: 'Failed Admin Login',
          userType: 'admin',
          ipAddress: '*********',
          userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4 hours ago
          success: false,
          details: { reason: 'Invalid password', attempts: 3 }
        },
        {
          id: '4',
          event: 'Password Change',
          userId: 'admin-1',
          userType: 'admin',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
          success: true
        },
        {
          id: '5',
          event: 'Multiple Failed Login Attempts',
          userType: 'student',
          ipAddress: '************',
          userAgent: 'curl/7.68.0',
          timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2), // 2 days ago
          success: false,
          details: { attempts: 10, blocked: true }
        }
      ];

      // Filter events based on time range
      const now = Date.now();
      const timeRangeMs = {
        '24h': 24 * 60 * 60 * 1000,
        '7d': 7 * 24 * 60 * 60 * 1000,
        '30d': 30 * 24 * 60 * 60 * 1000
      };

      const filteredEvents = mockEvents.filter(event => {
        const eventAge = now - event.timestamp.getTime();
        const inTimeRange = eventAge <= timeRangeMs[timeRange];
        
        const matchesFilter = filter === 'all' || 
                             (filter === 'success' && event.success) ||
                             (filter === 'failed' && !event.success);
        
        return inTimeRange && matchesFilter;
      });

      setEvents(filteredEvents);
    } catch (error) {
      console.error('Error loading security events:', error);
    } finally {
      setLoading(false);
    }
  };

  const getEventIcon = (event: SecurityEvent) => {
    if (!event.success) {
      return 'fas fa-exclamation-triangle text-red-500';
    }
    
    switch (event.event) {
      case 'Admin Login':
      case 'Student Login':
        return 'fas fa-sign-in-alt text-green-500';
      case 'Password Change':
        return 'fas fa-key text-blue-500';
      case 'Logout':
        return 'fas fa-sign-out-alt text-gray-500';
      default:
        return 'fas fa-info-circle text-blue-500';
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    
    if (diff < 60000) return 'منذ لحظات';
    if (diff < 3600000) return `منذ ${Math.floor(diff / 60000)} دقيقة`;
    if (diff < 86400000) return `منذ ${Math.floor(diff / 3600000)} ساعة`;
    return `منذ ${Math.floor(diff / 86400000)} يوم`;
  };

  const getSecurityScore = () => {
    const totalEvents = events.length;
    const failedEvents = events.filter(e => !e.success).length;
    
    if (totalEvents === 0) return { score: 100, level: 'ممتاز', color: 'text-green-600' };
    
    const successRate = ((totalEvents - failedEvents) / totalEvents) * 100;
    
    if (successRate >= 95) return { score: Math.round(successRate), level: 'ممتاز', color: 'text-green-600' };
    if (successRate >= 85) return { score: Math.round(successRate), level: 'جيد', color: 'text-blue-600' };
    if (successRate >= 70) return { score: Math.round(successRate), level: 'متوسط', color: 'text-yellow-600' };
    return { score: Math.round(successRate), level: 'ضعيف', color: 'text-red-600' };
  };

  if (!isOpen) return null;

  const securityScore = getSecurityScore();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                تدقيق الأمان
              </h3>
              <p className="text-gray-600 text-sm">
                مراجعة أحداث الأمان والوصول للنظام
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <i className="fas fa-times text-xl"></i>
            </button>
          </div>
        </div>

        {/* Security Score */}
        <div className="p-6 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-1">
                نقاط الأمان
              </h4>
              <p className="text-sm text-gray-600">
                تقييم عام لحالة أمان النظام
              </p>
            </div>
            <div className="text-center">
              <div className={`text-3xl font-bold ${securityScore.color}`}>
                {securityScore.score}%
              </div>
              <div className={`text-sm font-medium ${securityScore.color}`}>
                {securityScore.level}
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع الأحداث
              </label>
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as any)}
                className="form-input"
              >
                <option value="all">جميع الأحداث</option>
                <option value="success">الأحداث الناجحة</option>
                <option value="failed">الأحداث الفاشلة</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الفترة الزمنية
              </label>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as any)}
                className="form-input"
              >
                <option value="24h">آخر 24 ساعة</option>
                <option value="7d">آخر 7 أيام</option>
                <option value="30d">آخر 30 يوم</option>
              </select>
            </div>
          </div>
        </div>

        {/* Events List */}
        <div className="overflow-y-auto max-h-96">
          {loading ? (
            <div className="p-8 text-center">
              <i className="fas fa-spinner fa-spin text-gray-400 text-2xl mb-4"></i>
              <p className="text-gray-500">جاري تحميل أحداث الأمان...</p>
            </div>
          ) : events.length === 0 ? (
            <div className="p-8 text-center">
              <i className="fas fa-shield-alt text-gray-400 text-4xl mb-4"></i>
              <p className="text-gray-500">لا توجد أحداث أمان في الفترة المحددة</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {events.map((event) => (
                <div key={event.id} className="p-4 hover:bg-gray-50">
                  <div className="flex items-start gap-4">
                    <div className="flex-shrink-0">
                      <i className={`${getEventIcon(event)} text-lg`}></i>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h5 className="text-sm font-medium text-gray-900">
                          {event.event}
                        </h5>
                        <span className="text-xs text-gray-500">
                          {formatTimestamp(event.timestamp)}
                        </span>
                      </div>
                      
                      <div className="mt-1 text-sm text-gray-600">
                        <div className="flex items-center gap-4">
                          <span>IP: {event.ipAddress}</span>
                          <span>النوع: {event.userType === 'admin' ? 'مدير' : 'طالب'}</span>
                          {event.userId && <span>المعرف: {event.userId}</span>}
                        </div>
                      </div>
                      
                      {event.details && (
                        <div className="mt-2 text-xs text-gray-500">
                          {JSON.stringify(event.details, null, 2)}
                        </div>
                      )}
                      
                      <div className="mt-1 text-xs text-gray-400 truncate">
                        {event.userAgent}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              عرض {events.length} حدث من أحداث الأمان
            </div>
            <button
              onClick={onClose}
              className="btn btn-primary"
            >
              إغلاق
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityAudit;
