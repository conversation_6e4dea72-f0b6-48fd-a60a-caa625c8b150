# PowerShell script to deploy security rules after Firebase services are enabled
# Run this after completing Firebase setup

Write-Host "🔥 نشر قواعد الأمان لمنصة علاء عبد الحميد التعليمية" -ForegroundColor Cyan
Write-Host "==================================================" -ForegroundColor Cyan

Write-Host "📋 نشر قواعد Firestore..." -ForegroundColor Yellow
firebase deploy --only firestore:rules
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم نشر قواعد Firestore بنجاح" -ForegroundColor Green
} else {
    Write-Host "❌ فشل في نشر قواعد Firestore" -ForegroundColor Red
}

Write-Host ""
Write-Host "📁 نشر قواعد Storage..." -ForegroundColor Yellow
firebase deploy --only storage
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم نشر قواعد Storage بنجاح" -ForegroundColor Green
} else {
    Write-Host "❌ فشل في نشر قواعد Storage - تأكد من تفعيل Firebase Storage أولاً" -ForegroundColor Red
}

Write-Host ""
Write-Host "📊 نشر فهارس Firestore..." -ForegroundColor Yellow
firebase deploy --only firestore:indexes
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم نشر فهارس Firestore بنجاح" -ForegroundColor Green
} else {
    Write-Host "❌ فشل في نشر فهارس Firestore" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎉 انتهى نشر قواعد الأمان!" -ForegroundColor Green
Write-Host ""
Write-Host "🔗 روابط مهمة:" -ForegroundColor Cyan
Write-Host "• لوحة الإدارة: https://marketwise-academy-qhizq.web.app/alaa-admin-2024" -ForegroundColor White
Write-Host "• Firebase Console: https://console.firebase.google.com/project/marketwise-academy-qhizq" -ForegroundColor White
Write-Host ""
Write-Host "👤 بيانات المدير:" -ForegroundColor Cyan
Write-Host "• البريد الإلكتروني: <EMAIL>" -ForegroundColor White
Write-Host "• كلمة المرور: AlaaAdmin2024!" -ForegroundColor White

# Wait for user input before closing
Write-Host ""
Write-Host "اضغط أي مفتاح للإغلاق..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
