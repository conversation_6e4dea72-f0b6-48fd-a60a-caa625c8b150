import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import LoadingSpinner from '../LoadingSpinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: 'student' | 'admin' | 'any';
  fallbackPath?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = 'any',
  fallbackPath = '/login'
}) => {
  const { user, admin, loading } = useAuth();
  const location = useLocation();
  const [checking, setChecking] = useState(true);

  useEffect(() => {
    // Give a moment for auth to initialize
    const timer = setTimeout(() => {
      setChecking(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (loading || checking) {
    return <LoadingSpinner text="جاري التحقق من الصلاحيات..." />;
  }

  // Check authentication based on requirements
  const isAuthenticated = () => {
    switch (requireAuth) {
      case 'student':
        return !!user;
      case 'admin':
        return !!admin;
      case 'any':
        return !!(user || admin);
      default:
        return false;
    }
  };

  if (!isAuthenticated()) {
    // Store the attempted location for redirect after login
    const redirectPath = requireAuth === 'admin' 
      ? `/${import.meta.env.VITE_ADMIN_SECRET_PATH || 'admin'}`
      : fallbackPath;
    
    return (
      <Navigate 
        to={redirectPath} 
        state={{ from: location }} 
        replace 
      />
    );
  }

  return <>{children}</>;
};

export default ProtectedRoute;
