import { supabase, TABLES, handleSupabaseError } from './supabase';
import { signInWithEmailAndPassword, signOut } from 'firebase/auth';
import { auth } from './firebase';
import { User, Admin, AccessCode } from '../types';

// Local storage keys
const USER_STORAGE_KEY = 'alaa_elearning_user';
const ADMIN_STORAGE_KEY = 'alaa_elearning_admin';

// Student Authentication
export const loginWithAccessCode = async (accessCode: string): Promise<User> => {
  try {
    // Validate access code format (7 digits)
    if (!/^\d{7}$/.test(accessCode)) {
      throw new Error('كود الوصول يجب أن يكون 7 أرقام');
    }

    // Check if access code exists and is valid
    const { data: accessCodeData, error: accessCodeError } = await supabase
      .from(TABLES.ACCESS_CODES)
      .select(`
        *,
        users (*)
      `)
      .eq('code', accessCode)
      .eq('isUsed', true)
      .single();

    if (accessCodeError || !accessCodeData) {
      throw new Error('كود الوصول غير صحيح أو غير مفعل');
    }

    // Check if access code is expired
    if (accessCodeData.expiresAt && new Date(accessCodeData.expiresAt) < new Date()) {
      throw new Error('كود الوصول منتهي الصلاحية');
    }

    // Get user data
    const user = accessCodeData.users;
    if (!user || !user.isActive) {
      throw new Error('الحساب غير مفعل');
    }

    // Store user session
    localStorage.setItem(USER_STORAGE_KEY, JSON.stringify(user));

    return user;
  } catch (error: any) {
    console.error('Login error:', error);
    throw new Error(error.message || 'حدث خطأ أثناء تسجيل الدخول');
  }
};

export const getCurrentUser = async (): Promise<User | null> => {
  try {
    const storedUser = localStorage.getItem(USER_STORAGE_KEY);
    if (!storedUser) return null;

    const user = JSON.parse(storedUser);
    
    // Verify user still exists and is active
    const { data: userData, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', user.id)
      .eq('isActive', true)
      .single();

    if (error || !userData) {
      localStorage.removeItem(USER_STORAGE_KEY);
      return null;
    }

    return userData;
  } catch (error) {
    console.error('Get current user error:', error);
    localStorage.removeItem(USER_STORAGE_KEY);
    return null;
  }
};

export const logoutUser = async (): Promise<void> => {
  localStorage.removeItem(USER_STORAGE_KEY);
};

// Admin Authentication
export const adminLoginWithEmail = async (email: string, password: string): Promise<Admin> => {
  try {
    // Authenticate with Firebase
    await signInWithEmailAndPassword(auth, email, password);

    // Get admin data from Supabase
    const { data: adminData, error } = await supabase
      .from(TABLES.ADMINS)
      .select('*')
      .eq('email', email)
      .single();

    if (error || !adminData) {
      await signOut(auth);
      throw new Error('المستخدم غير مخول للوصول لوحة الإدارة');
    }

    // Store admin session
    localStorage.setItem(ADMIN_STORAGE_KEY, JSON.stringify(adminData));

    return adminData;
  } catch (error: any) {
    console.error('Admin login error:', error);
    throw new Error(error.message || 'حدث خطأ أثناء تسجيل دخول المدير');
  }
};

export const getCurrentAdmin = async (): Promise<Admin | null> => {
  try {
    const storedAdmin = localStorage.getItem(ADMIN_STORAGE_KEY);
    if (!storedAdmin) return null;

    const admin = JSON.parse(storedAdmin);
    
    // Verify admin still exists
    const { data: adminData, error } = await supabase
      .from(TABLES.ADMINS)
      .select('*')
      .eq('id', admin.id)
      .single();

    if (error || !adminData) {
      localStorage.removeItem(ADMIN_STORAGE_KEY);
      await signOut(auth);
      return null;
    }

    return adminData;
  } catch (error) {
    console.error('Get current admin error:', error);
    localStorage.removeItem(ADMIN_STORAGE_KEY);
    return null;
  }
};

export const logoutAdmin = async (): Promise<void> => {
  localStorage.removeItem(ADMIN_STORAGE_KEY);
  await signOut(auth);
};

export const checkAdminAuth = async (): Promise<boolean> => {
  const admin = await getCurrentAdmin();
  return !!admin;
};

// Access Code Management
export const generateAccessCodes = async (
  courseIds: string[],
  quantity: number,
  expiresAt?: Date,
  adminId?: string
): Promise<AccessCode[]> => {
  try {
    const accessCodes: Omit<AccessCode, 'id' | 'createdAt'>[] = [];

    for (let i = 0; i < quantity; i++) {
      const code = generateRandomCode();
      accessCodes.push({
        code,
        courseIds,
        isUsed: false,
        expiresAt,
        createdBy: adminId || 'system'
      });
    }

    const { data, error } = await supabase
      .from(TABLES.ACCESS_CODES)
      .insert(accessCodes)
      .select();

    if (error) {
      throw new Error(handleSupabaseError(error));
    }

    return data;
  } catch (error: any) {
    console.error('Generate access codes error:', error);
    throw new Error(error.message || 'حدث خطأ أثناء إنشاء أكواد الوصول');
  }
};

export const activateAccessCode = async (
  accessCode: string,
  studentName: string,
  studentEmail?: string
): Promise<User> => {
  try {
    // Check if access code exists and is not used
    const { data: accessCodeData, error: accessCodeError } = await supabase
      .from(TABLES.ACCESS_CODES)
      .select('*')
      .eq('code', accessCode)
      .eq('isUsed', false)
      .single();

    if (accessCodeError || !accessCodeData) {
      throw new Error('كود الوصول غير صحيح أو مستخدم بالفعل');
    }

    // Check if access code is expired
    if (accessCodeData.expiresAt && new Date(accessCodeData.expiresAt) < new Date()) {
      throw new Error('كود الوصول منتهي الصلاحية');
    }

    // Create new user
    const newUser: Omit<User, 'id' | 'createdAt' | 'updatedAt'> = {
      name: studentName,
      email: studentEmail,
      accessCode,
      isActive: true,
      enrolledCourses: accessCodeData.courseIds,
      certificates: []
    };

    const { data: userData, error: userError } = await supabase
      .from(TABLES.USERS)
      .insert([newUser])
      .select()
      .single();

    if (userError) {
      throw new Error(handleSupabaseError(userError));
    }

    // Mark access code as used
    const { error: updateError } = await supabase
      .from(TABLES.ACCESS_CODES)
      .update({
        isUsed: true,
        userId: userData.id,
        usedAt: new Date().toISOString()
      })
      .eq('id', accessCodeData.id);

    if (updateError) {
      console.error('Error updating access code:', updateError);
    }

    return userData;
  } catch (error: any) {
    console.error('Activate access code error:', error);
    throw new Error(error.message || 'حدث خطأ أثناء تفعيل كود الوصول');
  }
};

// Helper function to generate random 7-digit code
const generateRandomCode = (): string => {
  return Math.floor(1000000 + Math.random() * 9000000).toString();
};
