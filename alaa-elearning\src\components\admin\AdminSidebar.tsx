import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { Admin } from '../../types';

interface AdminSidebarProps {
  currentView: string;
  setCurrentView: (view: any) => void;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  admin: Admin;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({
  currentView,
  setCurrentView,
  isOpen,
  admin
}) => {
  const navigate = useNavigate();
  const { adminLogout } = useAuth();

  const handleLogout = async () => {
    try {
      await adminLogout();
      navigate('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };
  const menuItems = [
    {
      id: 'overview',
      label: 'نظرة عامة',
      icon: 'fas fa-chart-pie',
      description: 'إحصائيات المنصة'
    },
    {
      id: 'courses',
      label: 'إدارة الدورات',
      icon: 'fas fa-graduation-cap',
      description: 'إنشاء وإدارة الدورات'
    },
    {
      id: 'students',
      label: 'إدارة الطلاب',
      icon: 'fas fa-users',
      description: 'إدارة حسابات الطلاب'
    },
    {
      id: 'access-codes',
      label: 'أكواد الوصول',
      icon: 'fas fa-key',
      description: 'إنشاء وإدارة أكواد الوصول'
    },
    {
      id: 'certificates',
      label: 'إدارة الشهادات',
      icon: 'fas fa-certificate',
      description: 'قوالب وإصدار الشهادات'
    },
    {
      id: 'quizzes',
      label: 'إدارة الاختبارات',
      icon: 'fas fa-clipboard-question',
      description: 'إنشاء وإدارة الاختبارات'
    },
    {
      id: 'settings',
      label: 'الإعدادات',
      icon: 'fas fa-cog',
      description: 'إعدادات النظام'
    }
  ];

  return (
    <div className={`fixed right-0 top-0 h-full bg-gradient-to-b from-blue-900 to-blue-800 shadow-2xl transition-all duration-300 z-50 ${
      isOpen ? 'w-64' : 'w-16'
    }`}>
      {/* Header */}
      <div className="p-4 border-b border-blue-700">
        <div className="flex items-center gap-3">
          <div className="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center backdrop-blur-sm">
            <i className="fas fa-graduation-cap text-white text-xl"></i>
          </div>
          {isOpen && (
            <div>
              <h2 className="font-bold text-white text-sm">
                منصة علاء عبد الحميد
              </h2>
              <p className="text-xs text-blue-200">
                لوحة الإدارة
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="p-3 flex-1 overflow-y-auto">
        {menuItems.map((item) => (
          <button
            key={item.id}
            onClick={() => setCurrentView(item.id)}
            className={`w-full flex items-center gap-3 p-3 rounded-xl mb-2 transition-all duration-200 group relative ${
              currentView === item.id
                ? 'bg-white bg-opacity-20 text-white shadow-lg backdrop-blur-sm border border-white border-opacity-20'
                : 'text-blue-100 hover:bg-white hover:bg-opacity-10 hover:text-white'
            }`}
            title={!isOpen ? item.label : ''}
          >
            {/* Active Indicator */}
            {currentView === item.id && (
              <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-white rounded-l-full"></div>
            )}

            <i className={`${item.icon} text-lg ${
              currentView === item.id ? 'text-white' : 'text-blue-200'
            }`}></i>
            {isOpen && (
              <div className="text-right flex-1">
                <div className="font-semibold text-sm">
                  {item.label}
                </div>
                <div className={`text-xs ${
                  currentView === item.id ? 'text-blue-100' : 'text-blue-300'
                }`}>
                  {item.description}
                </div>
              </div>
            )}

            {/* Hover Effect */}
            {!isOpen && (
              <div className="absolute right-16 top-1/2 transform -translate-y-1/2 bg-gray-800 text-white px-2 py-1 rounded text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                {item.label}
              </div>
            )}
          </button>
        ))}
      </nav>

      {/* Footer */}
      {isOpen && (
        <div className="p-4 border-t border-blue-700 bg-blue-800 bg-opacity-50">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-sm">
                {admin.name.charAt(0)}
              </span>
            </div>
            <div className="flex-1">
              <div className="text-sm font-medium text-white">
                {admin.name}
              </div>
              <div className="text-xs text-blue-200">
                {admin.role === 'super_admin' ? 'مدير عام' : 'مدير'}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-3 flex gap-2">
            <button
              onClick={() => setCurrentView('settings')}
              className="flex-1 bg-white bg-opacity-10 hover:bg-opacity-20 text-white text-xs py-2 px-3 rounded-lg transition-all duration-200"
            >
              <i className="fas fa-cog ml-1"></i>
              الإعدادات
            </button>
            <button
              onClick={handleLogout}
              className="flex-1 bg-red-500 bg-opacity-80 hover:bg-opacity-100 text-white text-xs py-2 px-3 rounded-lg transition-all duration-200"
            >
              <i className="fas fa-sign-out-alt ml-1"></i>
              خروج
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminSidebar;
