import React from 'react';
import { Admin } from '../../types';

interface AdminSidebarProps {
  currentView: string;
  setCurrentView: (view: any) => void;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  admin: Admin;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({
  currentView,
  setCurrentView,
  isOpen,
  admin
}) => {
  const menuItems = [
    {
      id: 'overview',
      label: 'نظرة عامة',
      icon: 'fas fa-chart-pie',
      description: 'إحصائيات المنصة'
    },
    {
      id: 'courses',
      label: 'إدارة الدورات',
      icon: 'fas fa-graduation-cap',
      description: 'إنشاء وإدارة الدورات'
    },
    {
      id: 'students',
      label: 'إدارة الطلاب',
      icon: 'fas fa-users',
      description: 'إدارة حسابات الطلاب'
    },
    {
      id: 'access-codes',
      label: 'أكواد الوصول',
      icon: 'fas fa-key',
      description: 'إنشاء وإدارة أكواد الوصول'
    },
    {
      id: 'certificates',
      label: 'إدارة الشهادات',
      icon: 'fas fa-certificate',
      description: 'قوالب وإصدار الشهادات'
    },
    {
      id: 'quizzes',
      label: 'إدارة الاختبارات',
      icon: 'fas fa-clipboard-question',
      description: 'إنشاء وإدارة الاختبارات'
    },
    {
      id: 'settings',
      label: 'الإعدادات',
      icon: 'fas fa-cog',
      description: 'إعدادات النظام'
    }
  ];

  return (
    <div className={`fixed right-0 top-0 h-full bg-white shadow-lg border-l border-gray-200 transition-all duration-300 z-50 ${
      isOpen ? 'w-64' : 'w-16'
    }`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-primary-blue rounded-lg flex items-center justify-center">
            <i className="fas fa-graduation-cap text-white text-lg"></i>
          </div>
          {isOpen && (
            <div>
              <h2 className="font-bold text-gray-800 text-sm">
                منصة علاء عبد الحميد
              </h2>
              <p className="text-xs text-gray-500">
                لوحة الإدارة
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="p-2">
        {menuItems.map((item) => (
          <button
            key={item.id}
            onClick={() => setCurrentView(item.id)}
            className={`w-full flex items-center gap-3 p-3 rounded-lg mb-1 transition-all duration-200 group ${
              currentView === item.id
                ? 'bg-primary-blue text-white shadow-md'
                : 'text-gray-600 hover:bg-gray-100 hover:text-primary-blue'
            }`}
            title={!isOpen ? item.label : ''}
          >
            <i className={`${item.icon} text-lg ${
              currentView === item.id ? 'text-white' : 'text-gold'
            }`}></i>
            {isOpen && (
              <div className="text-right">
                <div className="font-medium text-sm">
                  {item.label}
                </div>
                <div className={`text-xs ${
                  currentView === item.id ? 'text-blue-100' : 'text-gray-500'
                }`}>
                  {item.description}
                </div>
              </div>
            )}
          </button>
        ))}
      </nav>

      {/* Footer */}
      {isOpen && (
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-center">
            <div className="text-xs text-gray-500 mb-1">
              مرحباً، {admin.name}
            </div>
            <div className="text-xs text-gray-400">
              {admin.role === 'super_admin' ? 'مدير عام' : 'مدير'}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminSidebar;
