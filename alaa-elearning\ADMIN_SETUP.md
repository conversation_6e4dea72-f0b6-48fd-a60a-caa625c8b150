# إعداد لوحة الإدارة - منصة علاء عبد الحميد التعليمية

## 🔥 إعداد Firebase (خطوات مطلوبة)

### 1. تفعيل Authentication
1. زيارة: https://console.firebase.google.com/project/marketwise-academy-qhizq/authentication
2. النقر على "Get started"
3. الذهاب إلى "Sign-in method"
4. تفعيل "Email/Password"
5. حفظ الإعدادات

### 2. إنشاء حساب المدير
1. في نفس صفحة Authentication
2. الذهاب إلى تبويب "Users"
3. النقر على "Add user"
4. إدخال البيانات:
   - **البريد الإلكتروني**: `<EMAIL>`
   - **كلمة المرور**: `AlaaAdmin2024!` (أو أي كلمة مرور قوية)
5. ا<PERSON>ن<PERSON><PERSON> على "Add user"

### 3. تفعيل Firestore Database
1. زيارة: https://console.firebase.google.com/project/marketwise-academy-qhizq/firestore
2. النقر على "Create database"
3. اختيار "Start in production mode"
4. اختيار المنطقة الأقرب (europe-west1 أو us-central1)
5. النقر على "Done"

### 4. تفعيل Storage
1. زيارة: https://console.firebase.google.com/project/marketwise-academy-qhizq/storage
2. النقر على "Get started"
3. اختيار "Start in production mode"
4. اختيار نفس المنطقة المختارة في Firestore
5. النقر على "Done"

## 📊 إعداد Supabase (اختياري - للمميزات المتقدمة)

### 1. إنشاء مشروع Supabase
1. زيارة: https://supabase.com/dashboard
2. النقر على "New project"
3. اختيار Organization
4. إدخال اسم المشروع: "Alaa E-Learning Platform"
5. إنشاء كلمة مرور قوية لقاعدة البيانات
6. اختيار المنطقة الأقرب
7. النقر على "Create new project"

### 2. تطبيق قاعدة البيانات
1. انتظار إنشاء المشروع (2-3 دقائق)
2. الذهاب إلى "SQL Editor"
3. نسخ محتوى ملف `database/supabase-schema.sql`
4. لصق المحتوى في المحرر
5. النقر على "Run" لتنفيذ السكريبت

### 3. الحصول على مفاتيح Supabase
1. الذهاب إلى Settings > API
2. نسخ:
   - **Project URL**: `https://your-project.supabase.co`
   - **anon/public key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

## 🔧 تحديث إعدادات المشروع

### تحديث ملف .env
```env
# Firebase Configuration (تحديث بالقيم الصحيحة)
VITE_FIREBASE_API_KEY=your_actual_api_key
VITE_FIREBASE_AUTH_DOMAIN=marketwise-academy-qhizq.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=marketwise-academy-qhizq
VITE_FIREBASE_STORAGE_BUCKET=marketwise-academy-qhizq.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=986244898137
VITE_FIREBASE_APP_ID=your_actual_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_actual_measurement_id

# Supabase Configuration (إذا تم الإعداد)
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_actual_anon_key

# Admin Configuration
VITE_ADMIN_SECRET_PATH=alaa-admin-2024

# Production
VITE_DEV_MODE=false
```

## 🚀 إعادة النشر

بعد تحديث الإعدادات:
```bash
npm run build
firebase deploy --only hosting
```

## 🔐 الوصول للوحة الإدارة

### الرابط:
```
https://marketwise-academy-qhizq.web.app/alaa-admin-2024
```

### بيانات تسجيل الدخول:
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `AlaaAdmin2024!` (أو ما تم اختياره)

## ✅ اختبار النظام

### 1. اختبار لوحة الإدارة:
1. زيارة رابط لوحة الإدارة
2. تسجيل الدخول بحساب المدير
3. التأكد من الوصول للوحة التحكم

### 2. اختبار إنشاء كود وصول:
1. إنشاء دورة تجريبية
2. إنشاء كود وصول 7 أرقام
3. اختبار تسجيل دخول طالب

## 🛡️ الأمان

### مستويات الحماية المطبقة:
- ✅ مسار إدارة مخفي
- ✅ مصادقة Firebase
- ✅ قواعد Firestore محكمة
- ✅ قواعد Storage آمنة
- ✅ Row Level Security في Supabase

### تغيير المسار المخفي:
لتغيير مسار لوحة الإدارة:
1. تحديث `VITE_ADMIN_SECRET_PATH` في ملف `.env`
2. إعادة البناء والنشر

## 📞 الدعم

في حالة مواجهة مشاكل:
1. التأكد من تفعيل جميع خدمات Firebase
2. مراجعة Console للأخطاء
3. التأكد من صحة متغيرات البيئة
4. فحص قواعد الأمان

---

**ملاحظة**: هذا الإعداد يوفر نظام إدارة كامل وآمن لمنصة علاء عبد الحميد التعليمية.
