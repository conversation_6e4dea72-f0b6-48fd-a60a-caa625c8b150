// User and Authentication Types
export interface User {
  id: string;
  email?: string;
  accessCode: string;
  name: string;
  isActive: boolean;
  enrolledCourses: string[];
  certificates: Certificate[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Admin {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'super_admin';
  createdAt: Date;
}

// Course Types
export interface Course {
  id: string;
  title: string;
  description: string;
  level: 1 | 2 | 3;
  sections: CourseSection[];
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CourseSection {
  id: string;
  courseId: string;
  title: string;
  description: string;
  order: number;
  videos: Video[];
  pdfs: PDFFile[];
  quiz?: Quiz;
  isActive: boolean;
  createdAt: Date;
}

export interface Video {
  id: string;
  sectionId: string;
  title: string;
  description?: string;
  url: string;
  duration: number; // in seconds
  order: number;
  isActive: boolean;
  createdAt: Date;
}

export interface PDFFile {
  id: string;
  sectionId: string;
  title: string;
  description?: string;
  url: string;
  fileName: string;
  fileSize: number;
  order: number;
  isActive: boolean;
  createdAt: Date;
}

// Assessment Types
export interface Quiz {
  id: string;
  sectionId: string;
  title: string;
  description?: string;
  questions: Question[];
  passingScore: number;
  timeLimit?: number; // in minutes
  isActive: boolean;
  createdAt: Date;
}

export interface Question {
  id: string;
  quizId: string;
  question: string;
  type: 'multiple_choice' | 'true_false' | 'short_answer';
  options?: string[]; // for multiple choice
  correctAnswer: string | number;
  points: number;
  order: number;
}

export interface QuizAttempt {
  id: string;
  userId: string;
  quizId: string;
  answers: QuizAnswer[];
  score: number;
  passed: boolean;
  startedAt: Date;
  completedAt?: Date;
}

export interface QuizAnswer {
  questionId: string;
  answer: string | number;
  isCorrect: boolean;
  points: number;
}

// Certificate Types
export interface Certificate {
  id: string;
  userId: string;
  courseId: string;
  templateId: string;
  studentName: string;
  courseName: string;
  issueDate: Date;
  certificateUrl: string;
  isActive: boolean;
}

export interface CertificateTemplate {
  id: string;
  name: string;
  imageUrl: string;
  width: number;
  height: number;
  textPositions: {
    studentName: { x: number; y: number; fontSize: number; color: string };
    courseName: { x: number; y: number; fontSize: number; color: string };
    date: { x: number; y: number; fontSize: number; color: string };
  };
  isActive: boolean;
  createdAt: Date;
}

// Access Code Types
export interface AccessCode {
  id: string;
  code: string;
  userId?: string;
  courseIds: string[];
  isUsed: boolean;
  expiresAt?: Date;
  createdBy: string;
  createdAt: Date;
  usedAt?: Date;
}

// Progress Tracking
export interface UserProgress {
  id: string;
  userId: string;
  courseId: string;
  sectionId: string;
  videoId?: string;
  completed: boolean;
  completedAt?: Date;
  lastAccessedAt: Date;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form Types
export interface LoginForm {
  accessCode: string;
}

export interface AdminLoginForm {
  email: string;
  password: string;
}

export interface CourseForm {
  title: string;
  description: string;
  level: 1 | 2 | 3;
}

export interface SectionForm {
  title: string;
  description: string;
  courseId: string;
}

export interface VideoForm {
  title: string;
  description?: string;
  file: File;
  sectionId: string;
}

export interface PDFForm {
  title: string;
  description?: string;
  file: File;
  sectionId: string;
}

export interface QuizForm {
  title: string;
  description?: string;
  sectionId: string;
  passingScore: number;
  timeLimit?: number;
  questions: QuestionForm[];
}

export interface QuestionForm {
  question: string;
  type: 'multiple_choice' | 'true_false' | 'short_answer';
  options?: string[];
  correctAnswer: string | number;
  points: number;
}

export interface AccessCodeForm {
  courseIds: string[];
  expiresAt?: Date;
  quantity: number;
}

// Navigation Types
export interface NavItem {
  label: string;
  path: string;
  icon: string;
  adminOnly?: boolean;
}

// Theme Types
export interface Theme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  border: string;
}

// Error Types
export interface AppError {
  code: string;
  message: string;
  details?: any;
}
