import React, { useState, useEffect } from 'react';
import { Course } from '../../types';
import LoadingSpinner from '../LoadingSpinner';

const CourseManagement: React.FC = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newCourse, setNewCourse] = useState({
    title: '',
    description: '',
    level: 1 as 1 | 2 | 3
  });

  useEffect(() => {
    loadCourses();
  }, []);

  const loadCourses = async () => {
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockCourses: Course[] = [
        {
          id: '1',
          title: 'أساسيات التسويق متعدد المستويات',
          description: 'دورة تأسيسية شاملة في مبادئ التسويق متعدد المستويات',
          level: 1,
          sections: [],
          isActive: true,
          createdBy: 'admin',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '2',
          title: 'استراتيجيات التسويق المتقدمة',
          description: 'تقنيات متقدمة في التسويق وبناء الفرق',
          level: 2,
          sections: [],
          isActive: true,
          createdBy: 'admin',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '3',
          title: 'القيادة والإدارة المتقدمة',
          description: 'مهارات القيادة وإدارة الأعمال التسويقية',
          level: 3,
          sections: [],
          isActive: true,
          createdBy: 'admin',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      
      setCourses(mockCourses);
    } catch (error) {
      console.error('Error loading courses:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCourse = async () => {
    try {
      // TODO: Replace with actual API call
      const course: Course = {
        id: Date.now().toString(),
        title: newCourse.title,
        description: newCourse.description,
        level: newCourse.level,
        sections: [],
        isActive: true,
        createdBy: 'admin',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      setCourses([...courses, course]);
      setNewCourse({ title: '', description: '', level: 1 });
      setShowCreateModal(false);
    } catch (error) {
      console.error('Error creating course:', error);
    }
  };

  const toggleCourseStatus = async (courseId: string) => {
    try {
      setCourses(courses.map(course => 
        course.id === courseId 
          ? { ...course, isActive: !course.isActive }
          : course
      ));
    } catch (error) {
      console.error('Error toggling course status:', error);
    }
  };

  const getLevelBadge = (level: number) => {
    const badges = {
      1: { text: 'المستوى الأول', color: 'bg-green-100 text-green-800' },
      2: { text: 'المستوى الثاني', color: 'bg-blue-100 text-blue-800' },
      3: { text: 'المستوى الثالث', color: 'bg-purple-100 text-purple-800' }
    };
    return badges[level as keyof typeof badges];
  };

  if (loading) {
    return <LoadingSpinner text="جاري تحميل الدورات..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الدورات</h1>
          <p className="text-gray-600">إنشاء وإدارة الدورات التدريبية</p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="btn btn-primary"
        >
          <i className="fas fa-plus"></i>
          إضافة دورة جديدة
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي الدورات</p>
              <p className="text-2xl font-bold text-gray-900">{courses.length}</p>
            </div>
            <i className="fas fa-graduation-cap text-blue-500 text-xl"></i>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">الدورات النشطة</p>
              <p className="text-2xl font-bold text-gray-900">
                {courses.filter(c => c.isActive).length}
              </p>
            </div>
            <i className="fas fa-check-circle text-green-500 text-xl"></i>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">المستوى الأول</p>
              <p className="text-2xl font-bold text-gray-900">
                {courses.filter(c => c.level === 1).length}
              </p>
            </div>
            <i className="fas fa-star text-yellow-500 text-xl"></i>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">المستوى المتقدم</p>
              <p className="text-2xl font-bold text-gray-900">
                {courses.filter(c => c.level === 3).length}
              </p>
            </div>
            <i className="fas fa-crown text-purple-500 text-xl"></i>
          </div>
        </div>
      </div>

      {/* Courses List */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">قائمة الدورات</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الدورة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المستوى
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الإنشاء
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {courses.map((course) => {
                const levelBadge = getLevelBadge(course.level);
                return (
                  <tr key={course.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {course.title}
                        </div>
                        <div className="text-sm text-gray-500">
                          {course.description}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${levelBadge.color}`}>
                        {levelBadge.text}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        course.isActive 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {course.isActive ? 'نشط' : 'غير نشط'}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {course.createdAt.toLocaleDateString('ar-EG')}
                    </td>
                    <td className="px-6 py-4 text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          className="text-blue-600 hover:text-blue-900"
                          title="تحرير"
                        >
                          <i className="fas fa-edit"></i>
                        </button>
                        <button
                          onClick={() => toggleCourseStatus(course.id)}
                          className={`${
                            course.isActive 
                              ? 'text-red-600 hover:text-red-900' 
                              : 'text-green-600 hover:text-green-900'
                          }`}
                          title={course.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                        >
                          <i className={`fas ${course.isActive ? 'fa-pause' : 'fa-play'}`}></i>
                        </button>
                        <button
                          className="text-purple-600 hover:text-purple-900"
                          title="إدارة المحتوى"
                        >
                          <i className="fas fa-folder-open"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Course Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              إضافة دورة جديدة
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="form-label">عنوان الدورة</label>
                <input
                  type="text"
                  value={newCourse.title}
                  onChange={(e) => setNewCourse({...newCourse, title: e.target.value})}
                  className="form-input"
                  placeholder="أدخل عنوان الدورة"
                />
              </div>
              
              <div>
                <label className="form-label">وصف الدورة</label>
                <textarea
                  value={newCourse.description}
                  onChange={(e) => setNewCourse({...newCourse, description: e.target.value})}
                  className="form-input"
                  rows={3}
                  placeholder="أدخل وصف الدورة"
                />
              </div>
              
              <div>
                <label className="form-label">المستوى</label>
                <select
                  value={newCourse.level}
                  onChange={(e) => setNewCourse({...newCourse, level: parseInt(e.target.value) as 1 | 2 | 3})}
                  className="form-input"
                >
                  <option value={1}>المستوى الأول - مبتدئ</option>
                  <option value={2}>المستوى الثاني - متوسط</option>
                  <option value={3}>المستوى الثالث - متقدم</option>
                </select>
              </div>
            </div>
            
            <div className="flex items-center gap-3 mt-6">
              <button
                onClick={handleCreateCourse}
                className="btn btn-primary"
                disabled={!newCourse.title || !newCourse.description}
              >
                إنشاء الدورة
              </button>
              <button
                onClick={() => setShowCreateModal(false)}
                className="btn btn-outline"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CourseManagement;
