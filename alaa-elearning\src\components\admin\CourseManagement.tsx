import React, { useState, useEffect } from 'react';
import { Course } from '../../types';
import LoadingSpinner from '../LoadingSpinner';

const CourseManagement: React.FC = () => {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newCourse, setNewCourse] = useState({
    title: '',
    description: '',
    level: 1 as 1 | 2 | 3
  });

  useEffect(() => {
    loadCourses();
  }, []);

  const loadCourses = async () => {
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockCourses: Course[] = [
        {
          id: '1',
          title: 'أساسيات التسويق متعدد المستويات',
          description: 'دورة تأسيسية شاملة في مبادئ التسويق متعدد المستويات',
          level: 1,
          sections: [],
          isActive: true,
          createdBy: 'admin',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '2',
          title: 'استراتيجيات التسويق المتقدمة',
          description: 'تقنيات متقدمة في التسويق وبناء الفرق',
          level: 2,
          sections: [],
          isActive: true,
          createdBy: 'admin',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '3',
          title: 'القيادة والإدارة المتقدمة',
          description: 'مهارات القيادة وإدارة الأعمال التسويقية',
          level: 3,
          sections: [],
          isActive: true,
          createdBy: 'admin',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];
      
      setCourses(mockCourses);
    } catch (error) {
      console.error('Error loading courses:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCourse = async () => {
    try {
      // TODO: Replace with actual API call
      const course: Course = {
        id: Date.now().toString(),
        title: newCourse.title,
        description: newCourse.description,
        level: newCourse.level,
        sections: [],
        isActive: true,
        createdBy: 'admin',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      setCourses([...courses, course]);
      setNewCourse({ title: '', description: '', level: 1 });
      setShowCreateModal(false);
    } catch (error) {
      console.error('Error creating course:', error);
    }
  };

  const toggleCourseStatus = async (courseId: string) => {
    try {
      setCourses(courses.map(course => 
        course.id === courseId 
          ? { ...course, isActive: !course.isActive }
          : course
      ));
    } catch (error) {
      console.error('Error toggling course status:', error);
    }
  };

  const getLevelBadge = (level: number) => {
    const badges = {
      1: { text: 'المستوى الأول', color: 'bg-green-100 text-green-800' },
      2: { text: 'المستوى الثاني', color: 'bg-blue-100 text-blue-800' },
      3: { text: 'المستوى الثالث', color: 'bg-purple-100 text-purple-800' }
    };
    return badges[level as keyof typeof badges];
  };

  if (loading) {
    return <LoadingSpinner text="جاري تحميل الدورات..." />;
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              إدارة الدورات
            </h1>
            <p className="text-gray-600 mt-2">إنشاء وإدارة الدورات التدريبية للمنصة</p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg"
          >
            <i className="fas fa-plus ml-2"></i>
            إضافة دورة جديدة
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg p-6 border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-blue-700">إجمالي الدورات</p>
              <p className="text-3xl font-bold text-blue-900">{courses.length}</p>
              <p className="text-xs text-blue-600 mt-1">جميع الدورات المتاحة</p>
            </div>
            <div className="w-14 h-14 bg-blue-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-graduation-cap text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl shadow-lg p-6 border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-green-700">الدورات النشطة</p>
              <p className="text-3xl font-bold text-green-900">
                {courses.filter(c => c.isActive).length}
              </p>
              <p className="text-xs text-green-600 mt-1">متاحة للطلاب</p>
            </div>
            <div className="w-14 h-14 bg-green-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-check-circle text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl shadow-lg p-6 border border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-yellow-700">المستوى الأول</p>
              <p className="text-3xl font-bold text-yellow-900">
                {courses.filter(c => c.level === 1).length}
              </p>
              <p className="text-xs text-yellow-600 mt-1">دورات تأسيسية</p>
            </div>
            <div className="w-14 h-14 bg-yellow-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-star text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl shadow-lg p-6 border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-purple-700">المستوى المتقدم</p>
              <p className="text-3xl font-bold text-purple-900">
                {courses.filter(c => c.level === 3).length}
              </p>
              <p className="text-xs text-purple-600 mt-1">دورات متقدمة</p>
            </div>
            <div className="w-14 h-14 bg-purple-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-crown text-white text-xl"></i>
            </div>
          </div>
        </div>
      </div>

      {/* Courses List */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">قائمة الدورات</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الدورة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  المستوى
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الإنشاء
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {courses.map((course) => {
                const levelBadge = getLevelBadge(course.level);
                return (
                  <tr key={course.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {course.title}
                        </div>
                        <div className="text-sm text-gray-500">
                          {course.description}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${levelBadge.color}`}>
                        {levelBadge.text}
                      </span>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        course.isActive 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {course.isActive ? 'نشط' : 'غير نشط'}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500">
                      {course.createdAt.toLocaleDateString('ar-EG')}
                    </td>
                    <td className="px-6 py-4 text-sm font-medium">
                      <div className="flex items-center gap-2">
                        <button
                          className="text-blue-600 hover:text-blue-900"
                          title="تحرير"
                        >
                          <i className="fas fa-edit"></i>
                        </button>
                        <button
                          onClick={() => toggleCourseStatus(course.id)}
                          className={`${
                            course.isActive 
                              ? 'text-red-600 hover:text-red-900' 
                              : 'text-green-600 hover:text-green-900'
                          }`}
                          title={course.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                        >
                          <i className={`fas ${course.isActive ? 'fa-pause' : 'fa-play'}`}></i>
                        </button>
                        <button
                          className="text-purple-600 hover:text-purple-900"
                          title="إدارة المحتوى"
                        >
                          <i className="fas fa-folder-open"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Course Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-lg">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-2xl">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-bold">إضافة دورة جديدة</h3>
                  <p className="text-blue-100 text-sm mt-1">إنشاء دورة تدريبية جديدة للمنصة</p>
                </div>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-200"
                >
                  <i className="fas fa-times text-white"></i>
                </button>
              </div>
            </div>

            {/* Modal Body */}
            <div className="p-6 space-y-6">
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <i className="fas fa-graduation-cap text-blue-600 ml-2"></i>
                  عنوان الدورة
                </label>
                <input
                  type="text"
                  value={newCourse.title}
                  onChange={(e) => setNewCourse({...newCourse, title: e.target.value})}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  placeholder="أدخل عنوان الدورة"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <i className="fas fa-align-left text-blue-600 ml-2"></i>
                  وصف الدورة
                </label>
                <textarea
                  value={newCourse.description}
                  onChange={(e) => setNewCourse({...newCourse, description: e.target.value})}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                  rows={4}
                  placeholder="أدخل وصف مفصل للدورة"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-2">
                  <i className="fas fa-layer-group text-blue-600 ml-2"></i>
                  مستوى الدورة
                </label>
                <select
                  value={newCourse.level}
                  onChange={(e) => setNewCourse({...newCourse, level: parseInt(e.target.value) as 1 | 2 | 3})}
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                >
                  <option value={1}>المستوى الأول - مبتدئ</option>
                  <option value={2}>المستوى الثاني - متوسط</option>
                  <option value={3}>المستوى الثالث - متقدم</option>
                </select>
              </div>

              {/* Level Description */}
              <div className="bg-gray-50 rounded-xl p-4">
                <h4 className="text-sm font-semibold text-gray-700 mb-2">وصف المستوى:</h4>
                <p className="text-sm text-gray-600">
                  {newCourse.level === 1 && "دورة تأسيسية للمبتدئين في مجال التسويق متعدد المستويات"}
                  {newCourse.level === 2 && "دورة متوسطة للذين لديهم خبرة أساسية في المجال"}
                  {newCourse.level === 3 && "دورة متقدمة للمحترفين والقادة في المجال"}
                </p>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="bg-gray-50 px-6 py-4 rounded-b-2xl flex items-center gap-3">
              <button
                onClick={handleCreateCourse}
                disabled={!newCourse.title || !newCourse.description}
                className="flex-1 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <i className="fas fa-plus ml-2"></i>
                إنشاء الدورة
              </button>
              <button
                onClick={() => setShowCreateModal(false)}
                className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-700 font-semibold py-3 px-6 rounded-xl transition-all duration-200"
              >
                <i className="fas fa-times ml-2"></i>
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CourseManagement;
