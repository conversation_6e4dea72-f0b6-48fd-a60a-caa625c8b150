import React, { useState, useEffect } from 'react';
import LoadingSpinner from '../LoadingSpinner';

interface DashboardStats {
  totalStudents: number;
  totalCourses: number;
  activeCodes: number;
  issuedCertificates: number;
  completedQuizzes: number;
  totalRevenue: number;
}

const DashboardOverview: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate loading dashboard stats
    const loadStats = async () => {
      try {
        // TODO: Replace with actual API calls
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        setStats({
          totalStudents: 156,
          totalCourses: 12,
          activeCodes: 45,
          issuedCertificates: 89,
          completedQuizzes: 234,
          totalRevenue: 15750
        });
      } catch (error) {
        console.error('Error loading dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    loadStats();
  }, []);

  if (loading) {
    return <LoadingSpinner text="جاري تحميل الإحصائيات..." />;
  }

  const statCards = [
    {
      title: 'إجمالي الطلاب',
      value: stats?.totalStudents || 0,
      icon: 'fas fa-users',
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'positive'
    },
    {
      title: 'الدورات المتاحة',
      value: stats?.totalCourses || 0,
      icon: 'fas fa-graduation-cap',
      color: 'bg-green-500',
      change: '+3',
      changeType: 'positive'
    },
    {
      title: 'أكواد الوصول النشطة',
      value: stats?.activeCodes || 0,
      icon: 'fas fa-key',
      color: 'bg-yellow-500',
      change: '-5',
      changeType: 'negative'
    },
    {
      title: 'الشهادات المصدرة',
      value: stats?.issuedCertificates || 0,
      icon: 'fas fa-certificate',
      color: 'bg-purple-500',
      change: '+18%',
      changeType: 'positive'
    },
    {
      title: 'الاختبارات المكتملة',
      value: stats?.completedQuizzes || 0,
      icon: 'fas fa-clipboard-check',
      color: 'bg-indigo-500',
      change: '+25%',
      changeType: 'positive'
    },
    {
      title: 'إجمالي الإيرادات',
      value: `${stats?.totalRevenue || 0} ج.م`,
      icon: 'fas fa-chart-line',
      color: 'bg-emerald-500',
      change: '+8%',
      changeType: 'positive'
    }
  ];

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 rounded-2xl p-8 text-white shadow-2xl relative overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -translate-y-16 translate-x-16"></div>
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-5 rounded-full translate-y-12 -translate-x-12"></div>

        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-3">
                مرحباً بك في لوحة الإدارة
              </h1>
              <p className="text-blue-100 text-lg">
                نظرة شاملة على أداء منصة علاء عبد الحميد التعليمية
              </p>
              <div className="mt-4 flex items-center gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <i className="fas fa-calendar text-blue-200"></i>
                  <span className="text-blue-100">اليوم: {new Date().toLocaleDateString('ar-EG')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <i className="fas fa-clock text-blue-200"></i>
                  <span className="text-blue-100">آخر تحديث: منذ دقائق</span>
                </div>
              </div>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-3">
                <i className="fas fa-chart-line text-white text-2xl"></i>
              </div>
              <div className="text-sm text-blue-100">إحصائيات اليوم</div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((card, index) => (
          <div key={index} className="bg-white rounded-xl shadow-lg p-6 border border-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm font-semibold text-gray-600 mb-2">
                  {card.title}
                </p>
                <p className="text-3xl font-bold text-gray-900 mb-3">
                  {card.value}
                </p>
                <div className="flex items-center">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    card.changeType === 'positive'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    <i className={`fas ${card.changeType === 'positive' ? 'fa-arrow-up' : 'fa-arrow-down'} ml-1`}></i>
                    {card.change}
                  </span>
                  <span className="text-xs text-gray-500 mr-2">
                    من الشهر الماضي
                  </span>
                </div>
              </div>
              <div className={`w-16 h-16 ${card.color} rounded-xl flex items-center justify-center shadow-lg`}>
                <i className={`${card.icon} text-white text-xl`}></i>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-500 ${card.color.replace('bg-', 'bg-').replace('-500', '-400')}`}
                  style={{ width: `${Math.min(100, (typeof card.value === 'number' ? card.value : 50))}%` }}
                ></div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <h3 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <i className="fas fa-bolt text-blue-600 ml-3"></i>
          إجراءات سريعة
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="group bg-gradient-to-br from-blue-50 to-blue-100 hover:from-blue-100 hover:to-blue-200 rounded-xl p-4 border border-blue-200 hover:shadow-lg transition-all duration-300 text-right">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-plus text-white text-lg"></i>
              </div>
              <div>
                <p className="font-semibold text-gray-900">إضافة دورة جديدة</p>
                <p className="text-sm text-gray-600">إنشاء دورة تدريبية</p>
              </div>
            </div>
          </button>

          <button className="group bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 rounded-xl p-4 border border-green-200 hover:shadow-lg transition-all duration-300 text-right">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-key text-white text-lg"></i>
              </div>
              <div>
                <p className="font-semibold text-gray-900">إنشاء كود وصول</p>
                <p className="text-sm text-gray-600">كود جديد للطلاب</p>
              </div>
            </div>
          </button>

          <button className="group bg-gradient-to-br from-purple-50 to-purple-100 hover:from-purple-100 hover:to-purple-200 rounded-xl p-4 border border-purple-200 hover:shadow-lg transition-all duration-300 text-right">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-certificate text-white text-lg"></i>
              </div>
              <div>
                <p className="font-semibold text-gray-900">إصدار شهادة</p>
                <p className="text-sm text-gray-600">شهادة لطالب</p>
              </div>
            </div>
          </button>

          <button className="group bg-gradient-to-br from-yellow-50 to-yellow-100 hover:from-yellow-100 hover:to-yellow-200 rounded-xl p-4 border border-yellow-200 hover:shadow-lg transition-all duration-300 text-right">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-yellow-500 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                <i className="fas fa-clipboard-question text-white text-lg"></i>
              </div>
              <div>
                <p className="font-semibold text-gray-900">إنشاء اختبار</p>
                <p className="text-sm text-gray-600">اختبار جديد</p>
              </div>
            </div>
          </button>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            النشاط الأخير
          </h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {[
              { action: 'تم تسجيل طالب جديد', user: 'أحمد محمد', time: 'منذ 5 دقائق', icon: 'fas fa-user-plus', color: 'text-green-600' },
              { action: 'تم إكمال اختبار المستوى الأول', user: 'فاطمة علي', time: 'منذ 15 دقيقة', icon: 'fas fa-check-circle', color: 'text-blue-600' },
              { action: 'تم إصدار شهادة', user: 'محمد حسن', time: 'منذ 30 دقيقة', icon: 'fas fa-certificate', color: 'text-purple-600' },
              { action: 'تم إنشاء كود وصول جديد', user: 'النظام', time: 'منذ ساعة', icon: 'fas fa-key', color: 'text-yellow-600' }
            ].map((activity, index) => (
              <div key={index} className="flex items-center gap-4">
                <div className={`w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center`}>
                  <i className={`${activity.icon} ${activity.color} text-sm`}></i>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.action}
                  </p>
                  <p className="text-xs text-gray-500">
                    {activity.user} • {activity.time}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardOverview;
