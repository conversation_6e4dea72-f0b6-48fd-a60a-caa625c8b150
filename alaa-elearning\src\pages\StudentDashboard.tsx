import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Course, UserProgress } from '../types';
import { getCourses } from '../services/courseService';
import LoadingSpinner from '../components/LoadingSpinner';
import { Link } from 'react-router-dom';

const StudentDashboard: React.FC = () => {
  const { user } = useAuth();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [, setProgress] = useState<UserProgress[]>([]);

  useEffect(() => {
    if (user) {
      loadStudentData();
    }
  }, [user]);

  const loadStudentData = async () => {
    try {
      // Load courses the student is enrolled in
      const allCourses = await getCourses();
      const enrolledCourses = allCourses.filter(course =>
        user?.enrolledCourses.includes(course.id)
      );
      setCourses(enrolledCourses);

      // TODO: Load user progress
      setProgress([]);
    } catch (error) {
      console.error('Error loading student data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getLevelBadge = (level: number) => {
    const badges = {
      1: { text: 'المستوى الأول', color: 'bg-green-100 text-green-800', icon: 'fas fa-seedling' },
      2: { text: 'المستوى الثاني', color: 'bg-blue-100 text-blue-800', icon: 'fas fa-tree' },
      3: { text: 'المستوى الثالث', color: 'bg-purple-100 text-purple-800', icon: 'fas fa-crown' }
    };
    return badges[level as keyof typeof badges];
  };

  const calculateCourseProgress = () => {
    // TODO: Calculate actual progress based on completed videos/sections
    return Math.floor(Math.random() * 100); // Mock progress for now
  };

  if (loading) {
    return <LoadingSpinner text="جاري تحميل لوحة الطالب..." />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Welcome Header */}
        <div className="bg-gradient-to-r from-primary-blue to-secondary-blue rounded-lg p-6 text-white mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">
                مرحباً، {user?.name}
              </h1>
              <p className="text-blue-100">
                استمر في رحلتك التعليمية وحقق أهدافك
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">{courses.length}</div>
              <div className="text-blue-100 text-sm">دورة مسجلة</div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الدورات المسجلة</p>
                <p className="text-2xl font-bold text-gray-900">{courses.length}</p>
              </div>
              <i className="fas fa-graduation-cap text-blue-500 text-2xl"></i>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الدورات المكتملة</p>
                <p className="text-2xl font-bold text-gray-900">
                  {courses.filter(() => calculateCourseProgress() === 100).length}
                </p>
              </div>
              <i className="fas fa-check-circle text-green-500 text-2xl"></i>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">الشهادات المحصلة</p>
                <p className="text-2xl font-bold text-gray-900">{user?.certificates.length || 0}</p>
              </div>
              <i className="fas fa-certificate text-gold text-2xl"></i>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">معدل التقدم</p>
                <p className="text-2xl font-bold text-gray-900">
                  {courses.length > 0
                    ? Math.round(courses.reduce((acc) => acc + calculateCourseProgress(), 0) / courses.length)
                    : 0}%
                </p>
              </div>
              <i className="fas fa-chart-line text-purple-500 text-2xl"></i>
            </div>
          </div>
        </div>

        {/* Enrolled Courses */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200 mb-8">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">دوراتي</h2>
          </div>

          {courses.length === 0 ? (
            <div className="p-8 text-center">
              <i className="fas fa-graduation-cap text-gray-400 text-4xl mb-4"></i>
              <h3 className="text-lg font-medium text-gray-700 mb-2">لا توجد دورات مسجلة</h3>
              <p className="text-gray-500">تواصل مع المدرب للحصول على كود وصول للدورات</p>
            </div>
          ) : (
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {courses.map((course) => {
                  const levelBadge = getLevelBadge(course.level);
                  const progressPercent = calculateCourseProgress();

                  return (
                    <div key={course.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow">
                      <div className="flex items-center justify-between mb-4">
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${levelBadge.color}`}>
                          <i className={`${levelBadge.icon} ml-1`}></i>
                          {levelBadge.text}
                        </span>
                        <div className="text-sm text-gray-500">
                          {progressPercent}% مكتمل
                        </div>
                      </div>

                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {course.title}
                      </h3>

                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                        {course.description}
                      </p>

                      {/* Progress Bar */}
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
                        <div
                          className="bg-primary-blue h-2 rounded-full transition-all duration-300"
                          style={{ width: `${progressPercent}%` }}
                        ></div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="text-sm text-gray-500">
                          {course.sections.length} قسم
                        </div>
                        <Link
                          to={`/course/${course.id}`}
                          className="btn btn-primary btn-sm"
                        >
                          {progressPercent === 0 ? 'ابدأ الدورة' : 'متابعة'}
                        </Link>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">النشاط الأخير</h2>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {[
                { action: 'بدأت دورة أساسيات التسويق', time: 'منذ يومين', icon: 'fas fa-play', color: 'text-green-600' },
                { action: 'أكملت القسم الأول', time: 'منذ 3 أيام', icon: 'fas fa-check', color: 'text-blue-600' },
                { action: 'حصلت على شهادة إتمام', time: 'منذ أسبوع', icon: 'fas fa-certificate', color: 'text-gold' }
              ].map((activity, index) => (
                <div key={index} className="flex items-center gap-4">
                  <div className={`w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center`}>
                    <i className={`${activity.icon} ${activity.color} text-sm`}></i>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.action}
                    </p>
                    <p className="text-xs text-gray-500">
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StudentDashboard;
