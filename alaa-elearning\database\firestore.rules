// Firestore Security Rules for Alaa E-Learning Platform
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isActiveUser() {
      return isAuthenticated() && 
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isActive == true;
    }
    
    // Users collection (Students)
    match /users/{userId} {
      allow read: if isOwner(userId) || isAdmin();
      allow create: if isAdmin();
      allow update: if isAdmin() || (isOwner(userId) && 
                                   !('isActive' in request.resource.data) &&
                                   !('enrolledCourses' in request.resource.data));
      allow delete: if isAdmin();
    }
    
    // Admins collection
    match /admins/{adminId} {
      allow read, write: if isAdmin();
    }
    
    // Courses collection
    match /courses/{courseId} {
      allow read: if isActiveUser() || isAdmin();
      allow write: if isAdmin();
      
      // Course sections subcollection
      match /sections/{sectionId} {
        allow read: if isActiveUser() || isAdmin();
        allow write: if isAdmin();
        
        // Videos subcollection
        match /videos/{videoId} {
          allow read: if isActiveUser() || isAdmin();
          allow write: if isAdmin();
        }
        
        // PDFs subcollection
        match /pdfs/{pdfId} {
          allow read: if isActiveUser() || isAdmin();
          allow write: if isAdmin();
        }
        
        // Quizzes subcollection
        match /quizzes/{quizId} {
          allow read: if isActiveUser() || isAdmin();
          allow write: if isAdmin();
          
          // Questions subcollection
          match /questions/{questionId} {
            allow read: if isActiveUser() || isAdmin();
            allow write: if isAdmin();
          }
        }
      }
    }
    
    // Quiz attempts collection
    match /quizAttempts/{attemptId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isActiveUser() && isOwner(request.resource.data.userId);
      allow update: if isOwner(resource.data.userId) || isAdmin();
      allow delete: if isAdmin();
    }
    
    // Quiz answers collection
    match /quizAnswers/{answerId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isActiveUser() && isOwner(request.resource.data.userId);
      allow update: if isOwner(resource.data.userId) || isAdmin();
      allow delete: if isAdmin();
    }
    
    // Certificates collection
    match /certificates/{certificateId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow write: if isAdmin();
    }
    
    // Certificate templates collection
    match /certificateTemplates/{templateId} {
      allow read: if isActiveUser() || isAdmin();
      allow write: if isAdmin();
    }
    
    // Access codes collection
    match /accessCodes/{codeId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
    }
    
    // User progress collection
    match /userProgress/{progressId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow create: if isActiveUser() && isOwner(request.resource.data.userId);
      allow update: if isOwner(resource.data.userId) || isAdmin();
      allow delete: if isAdmin();
    }
    
    // Analytics collection (admin only)
    match /analytics/{document=**} {
      allow read, write: if isAdmin();
    }
    
    // System settings collection (admin only)
    match /settings/{document=**} {
      allow read, write: if isAdmin();
    }
    
    // Notifications collection
    match /notifications/{notificationId} {
      allow read: if isOwner(resource.data.userId) || isAdmin();
      allow write: if isAdmin();
    }
    
    // Default deny rule for any other documents
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
