#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@grpc/proto-loader/build/bin/proto-loader-gen-types.js" "$@"
else 
  exec node  "$basedir/../@grpc/proto-loader/build/bin/proto-loader-gen-types.js" "$@"
fi
