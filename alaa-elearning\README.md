# منصة علاء عبد الحميد التعليمية

منصة تعليمية احترافية متخصصة في دورات التسويق متعددة المستويات مع دعم RTL وتكامل مزدوج مع قواعد البيانات.

## المميزات الرئيسية

### للطلاب
- 🔐 **نظام دخول بكود 7 أرقام**: دخول آمن وسهل للطلاب
- 📚 **دورات متعددة المستويات**: 3 مستويات تدريبية متدرجة
- 🎥 **محتوى فيديو عالي الجودة**: دروس فيديو احترافية
- 📄 **ملفات PDF**: مواد تعليمية إضافية
- 🏆 **شهادات معتمدة**: شهادات قابلة للتخصيص عند إتمام الدورات
- 📊 **اختبارات تفاعلية**: نظام تقييم شامل
- 📱 **تصميم متجاوب**: يعمل على جميع الأجهزة

### للمديرين
- 🛡️ **لوحة إدارة مخفية**: وصول آمن للمديرين فقط
- 👥 **إدارة الطلاب**: إنشاء وإدارة حسابات الطلاب
- 📋 **إدارة الدورات**: إنشاء وتنظيم المحتوى التعليمي
- 🎬 **رفع الفيديوهات**: رفع ومعالجة ملفات الفيديو
- 📎 **رفع ملفات PDF**: إدارة المواد التعليمية
- 🔑 **إنشاء أكواد الوصول**: توليد أكواد دخول للطلاب
- 🏅 **إصدار الشهادات**: إنشاء وإصدار شهادات مخصصة
- 📊 **نظام التقييم**: إنشاء وإدارة الاختبارات

## التقنيات المستخدمة

- **Frontend**: React 19 + TypeScript + Vite
- **Styling**: CSS مخصص مع دعم RTL
- **Database**: Supabase + Firebase (تكامل مزدوج)
- **Authentication**: Firebase Auth + Supabase Auth
- **Storage**: Firebase Storage
- **Hosting**: Firebase Hosting
- **Icons**: Font Awesome
- **Fonts**: Cairo (دعم العربية)

## التثبيت والإعداد

### 1. متطلبات النظام
```bash
Node.js >= 18
npm >= 9
```

### 2. تحميل المشروع
```bash
git clone <repository-url>
cd alaa-elearning
npm install
```

### 3. إعداد قواعد البيانات

#### إعداد Supabase
1. إنشاء مشروع جديد على [Supabase](https://supabase.com)
2. تشغيل السكريبت في `database/supabase-schema.sql`
3. نسخ URL و API Key

#### إعداد Firebase
1. إنشاء مشروع جديد على [Firebase Console](https://console.firebase.google.com)
2. تفعيل Authentication, Firestore, Storage, Hosting
3. رفع قواعد الأمان من مجلد `database/`
4. نسخ إعدادات المشروع

### 4. متغيرات البيئة
```bash
cp .env.example .env
```

تحديث الملف `.env` بالقيم الصحيحة:
```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_domain
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id

# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_anon_key

# Admin Configuration
VITE_ADMIN_SECRET_PATH=your_secret_admin_path
```

### 5. تشغيل المشروع
```bash
# Development
npm run dev

# Build
npm run build

# Preview
npm run preview
```

## البنية التنظيمية

```
alaa-elearning/
├── src/
│   ├── components/          # المكونات المشتركة
│   ├── pages/              # صفحات التطبيق
│   ├── services/           # خدمات قواعد البيانات
│   ├── types/              # تعريفات TypeScript
│   ├── utils/              # وظائف مساعدة
│   ├── styles/             # ملفات التنسيق
│   └── contexts/           # React Contexts
├── database/               # سكريبتات قواعد البيانات
├── public/                 # الملفات العامة
└── dist/                   # ملفات البناء
```

## الاستخدام

### للطلاب
1. زيارة الموقع الرئيسي
2. النقر على "تسجيل دخول"
3. إدخال كود الوصول (7 أرقام)
4. الوصول للدورات المتاحة

### للمديرين
1. زيارة `/admin-secret-path` (المسار المخفي)
2. تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
3. الوصول للوحة الإدارة الكاملة

## الأمان

- 🔒 **Row Level Security** في Supabase
- 🛡️ **Firebase Security Rules** محكمة
- 🔐 **مسار إدارة مخفي** قابل للتخصيص
- ✅ **التحقق من صحة البيانات** على جميع المستويات
- 🚫 **منع الوصول غير المصرح** به

## النشر

### Firebase Hosting
```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# تهيئة المشروع
firebase init

# النشر
npm run deploy
```

## الدعم والصيانة

- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +20 123 456 7890
- 🌐 **الموقع**: www.alaa-elearning.com

## الترخيص

جميع الحقوق محفوظة © 2025 منصة علاء عبد الحميد التعليمية

---

**ملاحظة**: هذا المشروع تم تطويره خصيصاً لمنصة علاء عبد الحميد التعليمية مع مراعاة جميع المتطلبات المحددة.
