{"version": 3, "names": ["toPrimitive", "input", "hint", "prim", "Symbol", "undefined", "res", "call", "TypeError", "String", "Number"], "sources": ["../../src/helpers/toPrimitive.ts"], "sourcesContent": ["/* @minVersion 7.1.5 */\n\n// https://tc39.es/ecma262/#sec-toprimitive\nexport default function toPrimitive(\n  input: unknown,\n  hint?: \"default\" | \"string\" | \"number\",\n) {\n  if (typeof input !== \"object\" || !input) return input;\n  // @ts-expect-error Symbol.toPrimitive might not index {}\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n"], "mappings": ";;;;;;AAGe,SAASA,WAAWA,CACjCC,KAAc,EACdC,IAAsC,EACtC;EACA,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,EAAE,OAAOA,KAAK;EAErD,IAAIE,IAAI,GAAGF,KAAK,CAACG,MAAM,CAACJ,WAAW,CAAC;EACpC,IAAIG,IAAI,KAAKE,SAAS,EAAE;IACtB,IAAIC,GAAG,GAAGH,IAAI,CAACI,IAAI,CAACN,KAAK,EAAEC,IAAI,IAAI,SAAS,CAAC;IAC7C,IAAI,OAAOI,GAAG,KAAK,QAAQ,EAAE,OAAOA,GAAG;IACvC,MAAM,IAAIE,SAAS,CAAC,8CAA8C,CAAC;EACrE;EACA,OAAO,CAACN,IAAI,KAAK,QAAQ,GAAGO,MAAM,GAAGC,MAAM,EAAET,KAAK,CAAC;AACrD", "ignoreList": []}