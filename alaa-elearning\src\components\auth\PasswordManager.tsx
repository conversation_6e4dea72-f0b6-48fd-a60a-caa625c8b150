import React, { useState } from 'react';
import { changeAdminPassword, resetPassword } from '../../services/authService';

interface PasswordManagerProps {
  isOpen: boolean;
  onClose: () => void;
  userEmail?: string;
}

const PasswordManager: React.FC<PasswordManagerProps> = ({
  isOpen,
  onClose,
  userEmail
}) => {
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showResetOption, setShowResetOption] = useState(false);

  const validatePassword = (password: string): boolean => {
    // Password must be at least 8 characters with uppercase, lowercase, number, and special character
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  };

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');

    // Validation
    if (!currentPassword || !newPassword || !confirmPassword) {
      setError('جميع الحقول مطلوبة');
      return;
    }

    if (newPassword !== confirmPassword) {
      setError('كلمة المرور الجديدة وتأكيدها غير متطابقين');
      return;
    }

    if (!validatePassword(newPassword)) {
      setError('كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل مع حرف كبير وصغير ورقم ورمز خاص');
      return;
    }

    if (currentPassword === newPassword) {
      setError('كلمة المرور الجديدة يجب أن تكون مختلفة عن الحالية');
      return;
    }

    setLoading(true);

    try {
      await changeAdminPassword(currentPassword, newPassword);
      setSuccess('تم تغيير كلمة المرور بنجاح');
      
      // Clear form
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      
      // Close modal after 2 seconds
      setTimeout(() => {
        onClose();
        setSuccess('');
      }, 2000);
    } catch (error: any) {
      setError(error.message || 'فشل في تغيير كلمة المرور');
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async () => {
    if (!userEmail) {
      setError('البريد الإلكتروني غير متوفر');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await resetPassword(userEmail);
      setSuccess('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني');
      setShowResetOption(false);
    } catch (error: any) {
      setError(error.message || 'فشل في إرسال رابط إعادة التعيين');
    } finally {
      setLoading(false);
    }
  };

  const getPasswordStrength = (password: string): { strength: number; text: string; color: string } => {
    let strength = 0;
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[@$!%*?&]/.test(password)) strength++;

    const levels = [
      { strength: 0, text: 'ضعيف جداً', color: 'text-red-600' },
      { strength: 1, text: 'ضعيف', color: 'text-red-500' },
      { strength: 2, text: 'متوسط', color: 'text-yellow-500' },
      { strength: 3, text: 'جيد', color: 'text-blue-500' },
      { strength: 4, text: 'قوي', color: 'text-green-500' },
      { strength: 5, text: 'قوي جداً', color: 'text-green-600' }
    ];

    return levels[strength];
  };

  if (!isOpen) return null;

  const passwordStrength = getPasswordStrength(newPassword);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            إدارة كلمة المرور
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>

        {!showResetOption ? (
          <form onSubmit={handleChangePassword} className="space-y-4">
            <div>
              <label className="form-label">كلمة المرور الحالية</label>
              <input
                type="password"
                value={currentPassword}
                onChange={(e) => setCurrentPassword(e.target.value)}
                className="form-input"
                placeholder="أدخل كلمة المرور الحالية"
                disabled={loading}
              />
            </div>

            <div>
              <label className="form-label">كلمة المرور الجديدة</label>
              <input
                type="password"
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                className="form-input"
                placeholder="أدخل كلمة المرور الجديدة"
                disabled={loading}
              />
              {newPassword && (
                <div className="mt-2">
                  <div className="flex items-center gap-2">
                    <div className="flex-1 bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-300 ${
                          passwordStrength.strength <= 1 ? 'bg-red-500' :
                          passwordStrength.strength <= 2 ? 'bg-yellow-500' :
                          passwordStrength.strength <= 3 ? 'bg-blue-500' :
                          'bg-green-500'
                        }`}
                        style={{ width: `${(passwordStrength.strength / 5) * 100}%` }}
                      ></div>
                    </div>
                    <span className={`text-xs ${passwordStrength.color}`}>
                      {passwordStrength.text}
                    </span>
                  </div>
                </div>
              )}
            </div>

            <div>
              <label className="form-label">تأكيد كلمة المرور الجديدة</label>
              <input
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="form-input"
                placeholder="أعد إدخال كلمة المرور الجديدة"
                disabled={loading}
              />
              {confirmPassword && newPassword !== confirmPassword && (
                <p className="text-red-500 text-xs mt-1">
                  كلمة المرور غير متطابقة
                </p>
              )}
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center">
                  <i className="fas fa-exclamation-triangle text-red-500 ml-2"></i>
                  <span className="text-red-700 text-sm">{error}</span>
                </div>
              </div>
            )}

            {success && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center">
                  <i className="fas fa-check-circle text-green-500 ml-2"></i>
                  <span className="text-green-700 text-sm">{success}</span>
                </div>
              </div>
            )}

            <div className="flex items-center gap-3 pt-4">
              <button
                type="submit"
                disabled={loading || !validatePassword(newPassword) || newPassword !== confirmPassword}
                className="btn btn-primary flex-1"
              >
                {loading ? (
                  <>
                    <i className="fas fa-spinner fa-spin ml-2"></i>
                    جاري التحديث...
                  </>
                ) : (
                  <>
                    <i className="fas fa-key ml-2"></i>
                    تغيير كلمة المرور
                  </>
                )}
              </button>
              
              <button
                type="button"
                onClick={() => setShowResetOption(true)}
                className="btn btn-outline"
                disabled={loading}
              >
                نسيت كلمة المرور؟
              </button>
            </div>
          </form>
        ) : (
          <div className="space-y-4">
            <div className="text-center">
              <i className="fas fa-envelope text-blue-500 text-4xl mb-4"></i>
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                إعادة تعيين كلمة المرور
              </h4>
              <p className="text-gray-600 text-sm mb-4">
                سيتم إرسال رابط إعادة تعيين كلمة المرور إلى:
              </p>
              <p className="font-medium text-gray-900 mb-6">
                {userEmail}
              </p>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center">
                  <i className="fas fa-exclamation-triangle text-red-500 ml-2"></i>
                  <span className="text-red-700 text-sm">{error}</span>
                </div>
              </div>
            )}

            {success && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                <div className="flex items-center">
                  <i className="fas fa-check-circle text-green-500 ml-2"></i>
                  <span className="text-green-700 text-sm">{success}</span>
                </div>
              </div>
            )}

            <div className="flex items-center gap-3">
              <button
                onClick={handleResetPassword}
                disabled={loading}
                className="btn btn-primary flex-1"
              >
                {loading ? (
                  <>
                    <i className="fas fa-spinner fa-spin ml-2"></i>
                    جاري الإرسال...
                  </>
                ) : (
                  <>
                    <i className="fas fa-paper-plane ml-2"></i>
                    إرسال الرابط
                  </>
                )}
              </button>
              
              <button
                onClick={() => setShowResetOption(false)}
                className="btn btn-outline"
                disabled={loading}
              >
                رجوع
              </button>
            </div>
          </div>
        )}

        {/* Password Requirements */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h5 className="text-sm font-medium text-gray-900 mb-2">
            متطلبات كلمة المرور:
          </h5>
          <ul className="text-xs text-gray-600 space-y-1">
            <li className="flex items-center">
              <i className="fas fa-check text-green-500 ml-2"></i>
              8 أحرف على الأقل
            </li>
            <li className="flex items-center">
              <i className="fas fa-check text-green-500 ml-2"></i>
              حرف كبير وحرف صغير
            </li>
            <li className="flex items-center">
              <i className="fas fa-check text-green-500 ml-2"></i>
              رقم واحد على الأقل
            </li>
            <li className="flex items-center">
              <i className="fas fa-check text-green-500 ml-2"></i>
              رمز خاص (@$!%*?&)
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default PasswordManager;
