import React, { useState, useEffect } from 'react';
import { User } from '../../types';
import LoadingSpinner from '../LoadingSpinner';

const StudentManagement: React.FC = () => {
  const [students, setStudents] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');

  useEffect(() => {
    loadStudents();
  }, []);

  const loadStudents = async () => {
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockStudents: User[] = [
        {
          id: '1',
          name: 'أحمد محمد علي',
          email: '<EMAIL>',
          accessCode: '1234567',
          isActive: true,
          enrolledCourses: ['1', '2'],
          certificates: [],
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date()
        },
        {
          id: '2',
          name: 'فاطمة حسن',
          email: '<EMAIL>',
          accessCode: '2345678',
          isActive: true,
          enrolledCourses: ['1'],
          certificates: [],
          createdAt: new Date('2024-02-10'),
          updatedAt: new Date()
        },
        {
          id: '3',
          name: 'محمد أحمد',
          email: '<EMAIL>',
          accessCode: '3456789',
          isActive: false,
          enrolledCourses: ['1', '2', '3'],
          certificates: [],
          createdAt: new Date('2024-01-20'),
          updatedAt: new Date()
        }
      ];
      
      setStudents(mockStudents);
    } catch (error) {
      console.error('Error loading students:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleStudentStatus = async (studentId: string) => {
    try {
      setStudents(students.map(student => 
        student.id === studentId 
          ? { ...student, isActive: !student.isActive }
          : student
      ));
    } catch (error) {
      console.error('Error toggling student status:', error);
    }
  };

  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.accessCode.includes(searchTerm);
    
    const matchesFilter = filterStatus === 'all' || 
                         (filterStatus === 'active' && student.isActive) ||
                         (filterStatus === 'inactive' && !student.isActive);
    
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return <LoadingSpinner text="جاري تحميل الطلاب..." />;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الطلاب</h1>
          <p className="text-gray-600">إدارة حسابات الطلاب وصلاحياتهم</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي الطلاب</p>
              <p className="text-2xl font-bold text-gray-900">{students.length}</p>
            </div>
            <i className="fas fa-users text-blue-500 text-xl"></i>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">الطلاب النشطون</p>
              <p className="text-2xl font-bold text-gray-900">
                {students.filter(s => s.isActive).length}
              </p>
            </div>
            <i className="fas fa-user-check text-green-500 text-xl"></i>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">طلاب جدد هذا الشهر</p>
              <p className="text-2xl font-bold text-gray-900">12</p>
            </div>
            <i className="fas fa-user-plus text-purple-500 text-xl"></i>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">معدل الإكمال</p>
              <p className="text-2xl font-bold text-gray-900">78%</p>
            </div>
            <i className="fas fa-chart-line text-yellow-500 text-xl"></i>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <i className="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              <input
                type="text"
                placeholder="البحث بالاسم، البريد الإلكتروني، أو كود الوصول..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pr-10"
              />
            </div>
          </div>
          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as 'all' | 'active' | 'inactive')}
              className="form-input"
            >
              <option value="all">جميع الطلاب</option>
              <option value="active">الطلاب النشطون</option>
              <option value="inactive">الطلاب غير النشطين</option>
            </select>
          </div>
        </div>
      </div>

      {/* Students List */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            قائمة الطلاب ({filteredStudents.length})
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الطالب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  كود الوصول
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الدورات المسجلة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ التسجيل
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredStudents.map((student) => (
                <tr key={student.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-primary-blue rounded-full flex items-center justify-center ml-3">
                        <span className="text-white font-medium">
                          {student.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {student.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {student.email || 'لا يوجد بريد إلكتروني'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                      {student.accessCode}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span className="text-sm text-gray-900">
                      {student.enrolledCourses.length} دورة
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      student.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {student.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {student.createdAt.toLocaleDateString('ar-EG')}
                  </td>
                  <td className="px-6 py-4 text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        className="text-blue-600 hover:text-blue-900"
                        title="عرض التفاصيل"
                      >
                        <i className="fas fa-eye"></i>
                      </button>
                      <button
                        className="text-green-600 hover:text-green-900"
                        title="تحرير"
                      >
                        <i className="fas fa-edit"></i>
                      </button>
                      <button
                        onClick={() => toggleStudentStatus(student.id)}
                        className={`${
                          student.isActive 
                            ? 'text-red-600 hover:text-red-900' 
                            : 'text-green-600 hover:text-green-900'
                        }`}
                        title={student.isActive ? 'إلغاء تفعيل' : 'تفعيل'}
                      >
                        <i className={`fas ${student.isActive ? 'fa-user-slash' : 'fa-user-check'}`}></i>
                      </button>
                      <button
                        className="text-purple-600 hover:text-purple-900"
                        title="إصدار شهادة"
                      >
                        <i className="fas fa-certificate"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredStudents.length === 0 && (
          <div className="text-center py-8">
            <i className="fas fa-users text-gray-400 text-4xl mb-4"></i>
            <p className="text-gray-500">لا توجد نتائج مطابقة للبحث</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentManagement;
