# دليل النشر - منصة علاء عبد الحميد التعليمية

## خطوات النشر الكاملة

### 1. إعداد Supabase

#### إنشاء المشروع
1. زيارة [Supabase Dashboard](https://supabase.com/dashboard)
2. إنشاء مشروع جديد
3. اختيار المنطقة الأقرب (Middle East أو Europe)
4. انتظار إنشاء المشروع (2-3 دقائق)

#### تطبيق قاعدة البيانات
1. الذهاب إلى SQL Editor في لوحة Supabase
2. نسخ محتوى `database/supabase-schema.sql`
3. تشغيل السكريبت
4. التأكد من إنشاء جميع الجداول بنجاح

#### الحصول على المفاتيح
1. الذهاب إلى Settings > API
2. نسخ:
   - Project URL
   - anon/public key

### 2. إعداد Firebase

#### إنشاء المشروع
1. زيارة [Firebase Console](https://console.firebase.google.com)
2. إنشاء مشروع جديد
3. تفعيل Google Analytics (اختياري)

#### تفعيل الخدمات المطلوبة

**Authentication:**
1. الذهاب إلى Authentication > Sign-in method
2. تفعيل Email/Password
3. إضافة مدير النظام:
   - البريد: <EMAIL>
   - كلمة المرور: (كلمة مرور قوية)

**Firestore Database:**
1. الذهاب إلى Firestore Database
2. إنشاء قاعدة بيانات في production mode
3. اختيار المنطقة الأقرب

**Storage:**
1. الذهاب إلى Storage
2. البدء في production mode
3. اختيار المنطقة الأقرب

**Hosting:**
1. الذهاب إلى Hosting
2. البدء

#### رفع قواعد الأمان
```bash
# تسجيل الدخول
firebase login

# تهيئة المشروع
firebase init

# اختيار:
# - Firestore
# - Storage  
# - Hosting

# رفع القواعد
firebase deploy --only firestore:rules
firebase deploy --only storage
```

#### الحصول على إعدادات المشروع
1. الذهاب إلى Project Settings
2. نسخ Firebase SDK configuration

### 3. إعداد متغيرات البيئة

إنشاء ملف `.env` في جذر المشروع:

```env
# Firebase Configuration
VITE_FIREBASE_API_KEY=AIzaSyC...
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef
VITE_FIREBASE_MEASUREMENT_ID=G-ABCDEF

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Admin Configuration (غير هذا المسار لأمان إضافي)
VITE_ADMIN_SECRET_PATH=admin-dashboard-secret-2024

# Development
VITE_DEV_MODE=false
```

### 4. بناء ونشر المشروع

#### البناء المحلي
```bash
# تثبيت التبعيات
npm install

# بناء المشروع
npm run build

# اختبار البناء محلياً
npm run preview
```

#### النشر على Firebase Hosting
```bash
# النشر
firebase deploy --only hosting

# أو النشر الكامل (قواعد + موقع)
firebase deploy
```

### 5. إعداد المدير الأول

#### في Firebase Authentication:
1. الذهاب إلى Authentication > Users
2. إضافة مستخدم جديد:
   - البريد: <EMAIL>
   - كلمة المرور: (كلمة مرور قوية)

#### في Supabase:
1. الذهاب إلى Table Editor > admins
2. إضافة سجل جديد:
   - name: علاء عبد الحميد
   - email: <EMAIL>
   - role: super_admin

### 6. اختبار النظام

#### اختبار لوحة الإدارة:
1. زيارة `https://your-domain.com/admin-dashboard-secret-2024`
2. تسجيل الدخول بحساب المدير
3. التأكد من الوصول للوحة الإدارة

#### اختبار إنشاء كود وصول:
1. إنشاء دورة تجريبية
2. إنشاء كود وصول للدورة
3. اختبار تسجيل دخول طالب بالكود

### 7. إعدادات الأمان الإضافية

#### Firebase Security Rules:
- التأكد من تطبيق قواعد Firestore
- التأكد من تطبيق قواعد Storage
- مراجعة صلاحيات Authentication

#### Supabase RLS:
- التأكد من تفعيل Row Level Security
- مراجعة السياسات المطبقة
- اختبار الصلاحيات

### 8. النسخ الاحتياطي

#### إعداد النسخ الاحتياطي التلقائي:
```bash
# Supabase backup
supabase db dump --db-url "postgresql://..." > backup.sql

# Firebase backup (يدوي من Console)
```

### 9. المراقبة والتحليلات

#### Firebase Analytics:
- مراجعة إحصائيات الاستخدام
- تتبع الأخطاء

#### Supabase Monitoring:
- مراقبة استخدام قاعدة البيانات
- مراجعة الأداء

### 10. الصيانة الدورية

#### أسبوعياً:
- مراجعة النسخ الاحتياطية
- فحص الأخطاء في Console

#### شهرياً:
- تحديث التبعيات
- مراجعة الأمان
- تحليل الاستخدام

---

## روابط مهمة

- **الموقع المنشور**: https://your-domain.com
- **لوحة الإدارة**: https://your-domain.com/admin-dashboard-secret-2024
- **Firebase Console**: https://console.firebase.google.com
- **Supabase Dashboard**: https://supabase.com/dashboard

## الدعم الفني

في حالة مواجهة مشاكل:
1. مراجعة Console للأخطاء
2. فحص متغيرات البيئة
3. التأكد من صحة قواعد الأمان
4. التواصل مع فريق الدعم

---

**تم إنشاء هذا الدليل خصيصاً لمنصة علاء عبد الحميد التعليمية**
